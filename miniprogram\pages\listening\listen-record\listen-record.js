Page({
  data: {
    currentStep: 'preview', // preview, listening1, listening2, listening3, preparation, retelling, result
    timeLeft: 60, // 倒计时时间（秒）
    isPlaying: false,
    isRecording: false,
    recordTime: 0,
    canFillBlanks: false,
    
    // 题目数据
    title: 'About Note Taking',
    
    // 答案相关
    answers: ['', '', '', ''],
    correctAnswers: ['lecture', 'organized', 'abbreviations', 'system'],
    filledCount: 0,
    
    // 评分相关
    fillingScore: 0,
    retellingScore: 0,
    totalScore: 0,
    
    // 转述相关
    retellingStartText: "I've learned some practical steps to improve our note-taking skills from Sam. First, we should focus on the main points rather than copying down the entire lecture or every word. Second, we need to write down keywords, dates, names, etc. quickly. Third, we should take visually clear, organized and structured notes. Fourth, we should use short forms and abbreviations, and write in signs and phrases instead of complete sentences. Lastly, we should be consistent with our structure and pick a system and stick with it.",
    
    // 定时器
    timer: null,
    audioTimer: null
  },

  onLoad() {
    this.startPreview();
  },

  onUnload() {
    this.clearAllTimers();
  },

  // 清除所有定时器
  clearAllTimers() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
      this.setData({ timer: null });
    }
    if (this.data.audioTimer) {
      clearTimeout(this.data.audioTimer);
      this.setData({ audioTimer: null });
    }
  },

  // 开始预览阶段
  startPreview() {
    this.setData({
      currentStep: 'preview',
      timeLeft: 60
    });
    
    this.startCountdown(() => {
      this.startListening1();
    });
  },

  // 开始倒计时
  startCountdown(callback) {
    this.clearAllTimers();
    
    const timer = setInterval(() => {
      const timeLeft = this.data.timeLeft - 1;
      this.setData({ timeLeft });
      
      if (timeLeft <= 0) {
        clearInterval(timer);
        this.setData({ timer: null });
        if (callback) callback();
      }
    }, 1000);
    
    this.setData({ timer });
  },

  // 第一遍听力
  startListening1() {
    this.setData({
      currentStep: 'listening1',
      canFillBlanks: true,
      isPlaying: true
    });
    
    // 模拟音频播放
    const audioTimer = setTimeout(() => {
      this.setData({ 
        isPlaying: false,
        audioTimer: null
      });
      
      // 短暂停顿后开始第二遍
      setTimeout(() => {
        this.startListening2();
      }, 2000);
    }, 8000); // 8秒音频
    
    this.setData({ audioTimer });
  },

  // 第二遍听力
  startListening2() {
    this.setData({
      currentStep: 'listening2',
      canFillBlanks: true,
      isPlaying: true
    });
    
    // 模拟音频播放
    const audioTimer = setTimeout(() => {
      this.setData({ 
        isPlaying: false,
        audioTimer: null
      });
      
      // 短暂停顿后开始第三遍
      setTimeout(() => {
        this.startListening3();
      }, 2000);
    }, 8000); // 8秒音频
    
    this.setData({ audioTimer });
  },

  // 第三遍听力（转述准备）
  startListening3() {
    this.setData({
      currentStep: 'listening3',
      canFillBlanks: false, // 第三遍不能填空
      isPlaying: true
    });
    
    // 模拟音频播放
    const audioTimer = setTimeout(() => {
      this.setData({ 
        isPlaying: false,
        audioTimer: null
      });
      
      // 短暂停顿后开始准备阶段
      setTimeout(() => {
        this.startPreparation();
      }, 2000);
    }, 8000); // 8秒音频
    
    this.setData({ audioTimer });
  },

  // 开始转述准备
  startPreparation() {
    this.setData({
      currentStep: 'preparation',
      timeLeft: 120 // 2分钟准备时间
    });
    
    this.startCountdown(() => {
      this.startRetelling();
    });
  },

  // 开始转述录音
  startRetelling() {
    this.setData({
      currentStep: 'retelling',
      timeLeft: 120, // 2分钟录音时间
      recordTime: 0,
      isRecording: true
    });
    
    // 录音时间计时
    const recordTimer = setInterval(() => {
      const recordTime = this.data.recordTime + 1;
      this.setData({ recordTime });
    }, 1000);
    
    // 总时间倒计时
    this.startCountdown(() => {
      clearInterval(recordTimer);
      this.finishRetelling();
    });
  },

  // 完成转述
  stopRetelling() {
    this.clearAllTimers();
    this.finishRetelling();
  },

  // 结束转述，显示结果
  finishRetelling() {
    this.setData({
      isRecording: false
    });
    
    // 计算分数
    this.calculateScores();
    
    // 显示结果
    this.setData({
      currentStep: 'result'
    });
  },

  // 计算分数
  calculateScores() {
    // 计算填空分数
    let fillingScore = 0;
    const { answers, correctAnswers } = this.data;
    const mistakes = [];
    
    for (let i = 0; i < answers.length; i++) {
      if (answers[i].trim().toLowerCase() === correctAnswers[i].toLowerCase()) {
        fillingScore += 1.5;
      } else {
        // 记录错误的填空
        mistakes.push({
          questionIndex: i + 1,
          userAnswer: answers[i].trim(),
          correctAnswer: correctAnswers[i]
        });
      }
    }
    
    // 添加错题到错题本
    if (mistakes.length > 0) {
      this.addMistakesToErrorBook(mistakes);
    }
    
    // 模拟转述分数（实际需要语音识别和评分）
    const retellingScore = Math.floor(Math.random() * 3) + 7; // 7-9分
    const totalScore = fillingScore + retellingScore;
    
    this.setData({
      fillingScore,
      retellingScore,
      totalScore
    });
  },

  // 填空输入处理
  onBlankInput(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;
    const answers = [...this.data.answers];
    answers[index] = value;
    
    // 计算已填写数量
    const filledCount = answers.filter(answer => answer.trim() !== '').length;
    
    this.setData({
      answers,
      filledCount
    });
  },

  // 格式化时间显示
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  },

  // 显示答案
  showAnswers() {
    const { correctAnswers } = this.data;
    wx.showModal({
      title: '参考答案',
      content: `1. ${correctAnswers[0]}\n2. ${correctAnswers[1]}\n3. ${correctAnswers[2]}\n4. ${correctAnswers[3]}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 显示听力原文
  showAudioText() {
    const audioText = `Note-taking is an essential skill for students and professionals alike. Here are some practical steps to improve your note-taking skills.

First, focus on the main points rather than copying down the entire lecture or every word. This will help you capture the key information without getting overwhelmed.

Second, write down keywords, dates, names, and other important details quickly. Speed is crucial in note-taking.

Third, take visually clear, organized and structured notes. This makes it easier to review and understand later.

Fourth, use short forms and abbreviations. Write in signs and phrases instead of complete sentences to save time.

Lastly, be consistent with your structure. Pick a system and stick with it throughout your note-taking process.`;

    wx.showModal({
      title: '听力原文',
      content: audioText,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 添加错题到错题本
  addMistakesToErrorBook(mistakes) {
    const app = getApp();
    
    // 使用更可靠的登录状态检查
    if (!app.canCollectMistakes()) {
      console.log('用户未登录或openid不可用，跳过错题收集');
      return;
    }
    
    const userId = app.getUserOpenId();

    mistakes.forEach(mistake => {
      const mistakeRecord = {
        title: this.data.title,
        questionIndex: mistake.questionIndex,
        question: `第${mistake.questionIndex}空`,
        userAnswer: mistake.userAnswer || '(未填写)',
        correctAnswer: mistake.correctAnswer,
        mistakeType: 'listening_record', // 标识来源为听后记录与转述
        section: 'filling_blanks', // 填空部分
        createTime: new Date()
      };

      wx.cloud.callFunction({
        name: 'addMistake',
        data: {
          userId: userId,
          wordId: `listen_record_${mistake.questionIndex}`,
          type: 'listening', // 归类到听口错题本
          extra: mistakeRecord
        }
      }).then(result => {
        console.log('听后记录错题已自动添加到错题本:', result);
      }).catch(error => {
        console.error('自动添加错题失败:', error);
        // 静默失败，不影响练习流程
      });
    });

    if (mistakes.length > 0) {
      console.log(`听后记录与转述完成，共收集到 ${mistakes.length} 个填空错题`);
    }
  }
}); 