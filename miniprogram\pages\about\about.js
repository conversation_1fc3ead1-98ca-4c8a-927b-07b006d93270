Page({
  data: {
    // 页面数据
    appVersion: 'V1.1.7' // 当前版本号，发布新版本时记得更新这里
  },

  onLoad() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '关于我们'
    })
  },

  // 复制邮箱
  copyEmail() {
    wx.setClipboardData({
      data: '<EMAIL>',
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        })
      }
    })
  },

  // 复制微信号
  copyWechat() {
    wx.setClipboardData({
      data: 'Mocizixishi',
      success: () => {
        wx.showToast({
          title: '微信号已复制',
          icon: 'success'
        })
      }
    })
  },

  // 手动检查更新
  checkForUpdate() {
    console.log('用户点击检查更新');
    
    try {
      const app = getApp();
      if (app && typeof app.manualCheckUpdate === 'function') {
        app.manualCheckUpdate();
      } else {
        // 如果app方法不可用，显示提示
        wx.showModal({
          title: '提示',
          content: '检查更新功能暂时不可用，请稍后重试。',
          showCancel: false,
          confirmText: '确定'
        });
      }
    } catch (error) {
      console.error('检查更新失败:', error);
      wx.showToast({
        title: '检查更新失败',
        icon: 'none',
        duration: 2000
      });
    }
  }
}) 