/* pages/profile/received/received.wxss */
.received-container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-bottom: 24rpx;
  padding: 36rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  color: white;
}

.header-content {
  text-align: center;
  margin-bottom: 24rpx;
}

.page-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 12rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.header-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
}

.header-stats .stat-item {
  text-align: center;
  flex: 1;
}

.header-stats .stat-number {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 4rpx;
}

.header-stats .stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
}

.input-container {
  position: relative;
  margin-bottom: 20rpx;
}

.share-id-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  padding: 0 100rpx 0 20rpx;
  font-size: 30rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.share-id-input:focus {
  border-color: #007AFF;
  background: #ffffff;
  box-shadow: 0 0 0 3rpx rgba(0, 122, 255, 0.1);
}

.clear-btn {
  position: absolute;
  right: 15rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: #ccc;
  color: white;
  font-size: 24rpx;
  line-height: 50rpx;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.clear-btn:active {
  background: #999;
}

.join-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: white;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.join-btn::after {
  border: none;
}

.join-btn:disabled {
  background: #e0e0e0;
  color: #999;
  box-shadow: none;
}

.join-btn:not(:disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24rpx 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #f0f0f0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.toolbar-btn {
  background: #f8f9fa;
  color: #333;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 10rpx 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 80rpx;
  max-width: 100rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.toolbar-btn:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.toolbar-btn.danger {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
  color: white;
  border-color: #FF3B30;
  padding: 10rpx 16rpx;
  min-width: 100rpx;
  max-width: 120rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
}

.toolbar-btn.danger:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #E6342A 0%, #FF5555 100%);
}

.shares-count {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
  background: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid #e9ecef;
}

/* 分享测试列表 */
.shares-section {
  margin-bottom: 20rpx;
}

.shares-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.share-item {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.share-item.expired {
  opacity: 0.7;
  background: #f8f9fa;
}

/* 主要内容区域 */
.share-main {
  display: flex;
  gap: 16rpx;
  align-items: flex-start;
}

/* 选择框 */
.share-checkbox {
  padding: 24rpx 0 0 24rpx;
  flex-shrink: 0;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #007AFF;
  border-color: #007AFF;
}

.checkbox-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

/* 分享内容 */
.share-content {
  padding: 24rpx 24rpx 0 0;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  flex: 1;
}

.share-header {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.share-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1.4;
}

.share-badges {
  display: flex;
  gap: 12rpx;
  align-items: center;
  flex-wrap: wrap;
}

.test-mode-badge {
  padding: 6rpx 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.share-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.share-status.active {
  background: rgba(76, 175, 80, 0.15);
  color: #4CAF50;
  border: 1rpx solid rgba(76, 175, 80, 0.3);
}

.share-status.expired {
  background: rgba(244, 67, 54, 0.15);
  color: #f44336;
  border: 1rpx solid rgba(244, 67, 54, 0.3);
}

.share-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.share-id {
  color: #7f8c8d;
  font-weight: 500;
}

.multi-level-tag {
  padding: 4rpx 12rpx;
  background: rgba(255, 193, 7, 0.15);
  color: #ff9800;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
  border: 1rpx solid rgba(255, 193, 7, 0.3);
}

.progress-indicator {
  padding: 4rpx 12rpx;
  background: rgba(76, 175, 80, 0.15);
  color: #4CAF50;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
  border: 1rpx solid rgba(76, 175, 80, 0.3);
}

/* 多关卡任务进度 */
.level-progress {
  background: #f0f9ff;
  padding: 15rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #007AFF;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background: #e0e0e0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF, #00C6FF);
  transition: width 0.3s ease;
}

/* 测试统计 */
.test-stats {
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 12rpx;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 24rpx;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #7f8c8d;
}

/* 多关卡进度 */
.level-progress {
  background: rgba(102, 126, 234, 0.1);
  padding: 12rpx 16rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.progress-text {
  font-size: 22rpx;
  color: #667eea;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.progress-bar {
  height: 6rpx;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 操作按钮 */
.share-actions {
  display: flex;
  gap: 12rpx;
  padding: 16rpx 24rpx 20rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #e8e8e8;
}

.action-btn {
  flex: 1;
  padding: 12rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  border: none;
  text-align: center;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.action-btn.primary:active {
  transform: translateY(1rpx);
}

.action-btn.disabled {
  background: #e0e0e0;
  color: #999;
  box-shadow: none;
}

.action-btn.secondary {
  background: white;
  color: #667eea;
  border: 1rpx solid #667eea;
}

.action-btn.secondary:active {
  background: rgba(102, 126, 234, 0.1);
}

/* 空状态 */
.empty-section {
  background: white;
  text-align: center;
  padding: 100rpx 40rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  margin-top: 40rpx;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 24rpx;
  opacity: 0.8;
}

.empty-text {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #7f8c8d;
  line-height: 1.6;
  max-width: 400rpx;
  margin: 0 auto;
}

/* 加载状态 */
.loading-section {
  background: white;
  text-align: center;
  padding: 80rpx 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.loading-text {
  font-size: 30rpx;
  color: #666;
}

/* 历史记录 */
.history-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.history-header {
  margin-bottom: 20rpx;
}

.history-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.history-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 15rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.history-item:active {
  border-color: #007AFF;
  background: #f0f9ff;
}

.history-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.history-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.test-score {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: bold;
}

.history-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-time {
  font-size: 24rpx;
  color: #666;
}

.rejoin-icon {
  font-size: 32rpx;
  color: #007AFF;
  margin-left: 20rpx;
}

/* 详情弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #666;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
}

.detail-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

/* 关卡详情 */
.level-detail {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e0e0e0;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.level-score-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.level-score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.level-name {
  font-size: 24rpx;
  color: #666;
}

.level-score {
  font-size: 24rpx;
  font-weight: bold;
  color: #007AFF;
}

/* 模式分组样式 */
.mode-groups {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 20rpx;
}

.mode-group {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 模式标题栏 */
.mode-header {
  padding: 25rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.mode-header:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.mode-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.mode-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.mode-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  margin-right: 8rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

.mode-count {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  flex-shrink: 0;
}

.mode-stats {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin: 0 8rpx;
  flex-shrink: 1;
  min-width: 0;
  flex: 1;
  justify-content: flex-end;
}

.mode-stats .stat {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.mode-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-shrink: 0;
}

.expand-icon {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: transform 0.3s ease;
  margin-left: 8rpx;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.shares-list {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: white;
}

.shares-list.expanded {
  max-height: 2000rpx;
}