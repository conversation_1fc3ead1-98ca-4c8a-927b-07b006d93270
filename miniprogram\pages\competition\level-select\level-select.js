Page({
  data: {
    masterCompetitionId: '',
    competitionName: '',
    mode: '',
    modeName: '',
    levels: [], // 当前页显示的关卡
    allLevels: [], // 所有关卡数据
    loading: true,
    totalWords: 0,
    totalLevels: 0,
    averageScore: 0,
    totalParticipants: 0,

    // 分页相关
    currentPage: 1,
    pageSize: 10,
    totalPages: 0,
    hasMore: false,
    showPageInputModal: false
  },

  async onLoad(options) {
    const { masterCompetitionId, mode, competitionName } = options;
    
    if (!masterCompetitionId || !mode) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    const modeNames = {
      'elimination': '消消乐',
      'en2zh': '英译汉',
      'zh2en': '汉译英',
      'dictation': '听写'
    };

    this.setData({
      masterCompetitionId,
      mode,
      modeName: modeNames[mode] || '单词竞赛',
      competitionName: decodeURIComponent(competitionName || '多关卡竞赛')
    });

    await this.loadGroupedCompetitionDetail();
  },

  async loadGroupedCompetitionDetail() {
    this.setData({ loading: true });

    try {
      const result = await wx.cloud.callFunction({
        name: 'getCompetitions',
        data: {
          mode: 'getGroupedDetail',
          masterCompetitionId: this.data.masterCompetitionId
        }
      });

      if (result.result.success) {
        const data = result.result.data;
        
        // 获取用户进度，确定哪些关卡已解锁
        const userProgress = data.userProgress || {};
        const app = getApp();
        const userInfo = app.globalData.userInfo;
        let currentUserOpenId = null;
        
        // 尝试多种方式获取用户OpenID
        if (userInfo && userInfo.openid) {
          currentUserOpenId = userInfo.openid;
        } else if (app.globalData.openid) {
          currentUserOpenId = app.globalData.openid;
        } else {
          // 尝试从本地存储获取
          try {
            const storedUserInfo = wx.getStorageSync('userInfo');
            const storedOpenId = wx.getStorageSync('openid');
            if (storedUserInfo && storedUserInfo.openid) {
              currentUserOpenId = storedUserInfo.openid;
            } else if (storedOpenId) {
              currentUserOpenId = storedOpenId;
            }
          } catch (error) {
            console.error('获取存储的openid失败:', error);
          }
        }
        
        const userCompletedLevels = currentUserOpenId ? (userProgress[currentUserOpenId] || []) : [];
        
        console.log('=== 关卡解锁状态计算 ===');
        console.log('当前用户OpenID:', currentUserOpenId);
        console.log('用户信息:', userInfo);
        console.log('用户完成的关卡ID列表:', userCompletedLevels);
        console.log('所有关卡信息:', data.levels.map(l => ({ id: l.id, levelNumber: l.levelNumber, name: l.name })));
        
        // 处理关卡锁定状态
        const processedLevels = data.levels.map((level, index) => {
          const isFirstLevel = index === 0;
          const previousLevelCompleted = index > 0 && userCompletedLevels.includes(data.levels[index - 1].id);
          const isCompleted = userCompletedLevels.includes(level.id);
          
          // 如果没有获取到用户OpenID，只有第一关解锁，其他关卡锁定
          let locked;
          if (!currentUserOpenId) {
            locked = !isFirstLevel; // 没有openid时，只有第一关解锁
          } else {
            locked = !isFirstLevel && !previousLevelCompleted && !isCompleted;
          }
          
          console.log(`关卡 ${level.levelNumber}:`, {
            isFirstLevel,
            previousLevelCompleted,
            isCompleted,
            locked,
            previousLevelId: index > 0 ? data.levels[index - 1].id : 'N/A'
          });
          
          return {
            ...level,
            locked: locked,
            completed: isCompleted
          };
        });
        
        console.log('=== 最终关卡状态 ===');
        processedLevels.forEach(level => {
          console.log(`关卡 ${level.levelNumber}: ${level.locked ? '🔒锁定' : '🔓解锁'} ${level.completed ? '✅已完成' : '❌未完成'}`);
        });

        // 计算分页信息
        const totalPages = Math.ceil(processedLevels.length / this.data.pageSize);

        this.setData({
          allLevels: processedLevels,
          totalWords: data.totalWords,
          totalLevels: data.totalLevels,
          averageScore: data.averageScore,
          totalParticipants: data.totalParticipants,
          totalPages: totalPages,
          loading: false
        });

        // 加载第一页数据
        this.loadPage(1);
      } else {
        throw new Error(result.result.message || '加载失败');
      }
    } catch (error) {
      console.error('加载分组竞赛详情失败:', error);
      wx.showModal({
        title: '加载失败',
        content: '竞赛不存在或已过期',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 加载指定页的关卡数据
  loadPage(page) {
    const { allLevels, pageSize, totalPages } = this.data;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = allLevels.slice(startIndex, endIndex);

    // 生成页码数组
    let pageNumbers = [];
    if (totalPages <= 7) {
      // 总页数不超过7页，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // 总页数超过7页，显示当前页前后各2页，加上首页和末页
      let start = Math.max(2, page - 2);
      let end = Math.min(totalPages - 1, page + 2);

      // 始终显示第1页
      pageNumbers.push(1);

      // 如果start > 2，添加省略号标识
      if (start > 2) {
        pageNumbers.push('...');
      }

      // 添加中间页码
      for (let i = start; i <= end; i++) {
        if (i !== 1 && i !== totalPages) {
          pageNumbers.push(i);
        }
      }

      // 如果end < totalPages - 1，添加省略号标识
      if (end < totalPages - 1) {
        pageNumbers.push('...');
      }

      // 始终显示最后一页
      if (totalPages > 1) {
        pageNumbers.push(totalPages);
      }
    }

    console.log(`加载第${page}页关卡:`, {
      page,
      startIndex,
      endIndex,
      pageData: pageData.length,
      totalLevels: allLevels.length,
      pageNumbers
    });

    this.setData({
      levels: pageData,
      currentPage: page,
      hasMore: endIndex < allLevels.length,
      pageNumbers: pageNumbers
    });
  },

  // 上一页
  onPrevPage() {
    if (this.data.currentPage > 1) {
      this.loadPage(this.data.currentPage - 1);
    }
  },

  // 下一页
  onNextPage() {
    if (this.data.currentPage < this.data.totalPages) {
      this.loadPage(this.data.currentPage + 1);
    }
  },

  // 跳转到指定页
  onGoToPage(e) {
    const page = parseInt(e.currentTarget.dataset.page);
    if (page >= 1 && page <= this.data.totalPages && page !== this.data.currentPage) {
      this.loadPage(page);
    }
  },

  // 显示页码输入框
  showPageInput() {
    this.setData({
      showPageInputModal: true
    });
  },

  // 页码输入确认
  onPageInputConfirm(e) {
    const { page } = e.detail;
    this.loadPage(page);
  },

  // 页码输入取消
  onPageInputCancel() {
    this.setData({
      showPageInputModal: false
    });
  },

  async onLevelTap(e) {
    const levelId = e.currentTarget.dataset.id;
    const level = this.data.levels.find(l => l.id === levelId);
    
    if (!level) {
      wx.showToast({
        title: '关卡不存在',
        icon: 'error'
      });
      return;
    }
    
    // 检查关卡是否锁定
    if (level.locked) {
      wx.showToast({
        title: `请先完成第${level.levelNumber - 1}关`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 检查用户登录状态
    const app = getApp();
    if (!app.isLoggedIn()) {
      wx.showModal({
        title: '需要登录',
        content: '参与竞赛需要登录，是否立即登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    wx.showLoading({
      title: '加载关卡...',
      mask: true
    });

    try {
      // 获取关卡详情
      const result = await wx.cloud.callFunction({
        name: 'getCompetitionDetail',
        data: {
          competitionId: levelId
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        const levelData = result.result.data;
        
        // 根据不同模式跳转到不同页面
        if (this.data.mode === 'elimination') {
          // 消消乐模式
          const app = getApp();
          const gameWords = levelData.words.map(word => ({
            english: word.words || word.word || word.english,
            chinese: word.meaning || word.chinese || '无释义'
          }));
          
          app.globalData.eliminationGameData = {
            words: gameWords,
            currentGroup: 1,
            totalGroups: 1,
            libraryId: levelData.libraryId,
            libraryName: levelData.libraryName,
            competitionMode: true,
            competitionId: levelId,
            masterCompetitionId: this.data.masterCompetitionId,
            levelNumber: level.levelNumber,
            totalLevels: this.data.totalLevels
          };
          
          wx.navigateTo({
            url: `/pages/task/puzzle/puzzle?competitionId=${levelId}&mode=custom&gameMode=${levelData.gameMode || '60'}&masterCompetitionId=${this.data.masterCompetitionId}`
          });
        } else if (this.data.mode === 'dictation') {
          // 听写模式
          wx.navigateTo({
            url: `/pages/spelling/practice/practice?competitionId=${levelId}&mode=competition&practiceMode=test&masterCompetitionId=${this.data.masterCompetitionId}`
          });
        } else {
          // 英译汉/汉译英模式
          const app = getApp();
          app.globalData.learningData = {
            words: levelData.words,
            libraryId: levelData.libraryId,
            libraryName: levelData.libraryName,
            masterCompetitionId: this.data.masterCompetitionId,
            levelNumber: level.levelNumber,
            totalLevels: this.data.totalLevels
          };
          
          wx.navigateTo({
            url: `/pages/wordtest/test/test?competitionId=${levelId}&mode=${this.data.mode}&shareMode=competition&masterCompetitionId=${this.data.masterCompetitionId}`
          });
        }
      } else {
        throw new Error(result.result.message || '获取关卡详情失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载关卡失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  onViewOverallRanking() {
    // 查看整体排行榜（跨所有关卡）
    wx.navigateTo({
      url: `/pages/competition/ranking/ranking?competitionId=${this.data.masterCompetitionId}&mode=${this.data.mode}&type=master`
    });
  },

  onBackToCompetition() {
    wx.navigateBack();
  },

  // 刷新关卡状态（用于外部调用）
  refreshLevelStatus() {
    console.log('外部触发关卡状态刷新');
    this.loadGroupedCompetitionDetail();
  },

  // 页面显示时检查是否需要刷新
  onShow() {
    const app = getApp();
    if (app.globalData && app.globalData.needRefreshLevelSelect) {
      console.log('检测到需要刷新关卡状态');
      app.globalData.needRefreshLevelSelect = false;
      setTimeout(() => {
        this.loadGroupedCompetitionDetail();
      }, 500); // 延迟500ms确保数据已经更新
    }
  },

  onShareAppMessage() {
    return {
      title: `${this.data.competitionName} - ${this.data.modeName}多关卡竞赛`,
      path: `/pages/competition/level-select/level-select?masterCompetitionId=${this.data.masterCompetitionId}&mode=${this.data.mode}&competitionName=${encodeURIComponent(this.data.competitionName)}`,
      imageUrl: '/assets/icons/logo.png'
    };
  }
}); 