const app = getApp();
const examLock = require('../../../utils/exam-lock');
const optionGenerator = require('../../../utils/option-generator');

Page({
  data: {
    // 测试模式相关
    testMode: '', // en_to_cn, cn_to_en
    isTestMode: true, // 区分测试模式和练习模式
    isPhrase: false, // 是否为短语测试模式
    timeLimit: 30, // 时间限制（分钟，保持兼容性）
    timeLeft: 0, // 剩余时间（秒）
    timer: null,
    
    // 每题时间限制
    perQuestionTime: 15, // 每题时间限制（秒）
    currentQuestionTimeLeft: 0, // 当前题目剩余时间（秒）
    questionTimer: null, // 每题计时器
    
    // 词汇数据
    words: [],
    currentIndex: 0,
    currentWord: null,
    options: [],
    
    // 答题状态
    isAnswered: false,
    selectedOption: -1,
    correctOption: -1,
    showAnswer: false,

    // 汉译英填空题相关
    userInput: '', // 用户输入的答案
    showResult: false, // 是否显示答案结果
    isCorrect: false, // 当前答案是否正确
    inputFocused: false, // 输入框是否聚焦
    
    // 统计数据
    totalQuestions: 0,
    correctCount: 0,
    wrongCount: 0,
    score: 0,
    accuracyRate: '0.0', // 准确率（字符串格式，已格式化）
    progressPercent: 0, // 进度百分比
    timeDisplay: '00:00', // 时间显示
    
    // 错题记录
    mistakes: [],
    
    // 分享相关
    shareMode: 'self', // 'self' or 'share'
    shareId: '',
    
    // 测试状态
    testStarted: false,
    testCompleted: false,
    showResult: false,
    
    // 其他状态
    startTime: null,
    endTime: null,
    libraryId: '',
    libraryName: '',
    
    // 练习模式状态
    showMeaning: false, // 是否显示释义
    showExample: false,  // 是否显示例句
    
    // 发音相关
    audioContext: null, // 音频上下文
    isPlaying: false, // 是否正在播放
    
    // 分享弹窗相关
    showShareModal: false, // 是否显示分享弹窗
    currentShareData: null, // 当前分享数据
    
    // 多关卡任务相关
    isMultiLevel: false, // 是否为多关卡任务
    currentLevelId: 1, // 当前关卡ID
    totalLevels: 1, // 总关卡数
    levelProgress: null, // 当前用户的关卡进度
    allLevels: [], // 所有关卡数据
    shareTestData: null, // 完整的分享测试数据

    // 重定向控制
    isRedirecting: true // 初始为true，防止页面闪现
  },

  onLoad: function(options) {
    const { testMode, timeLimit, shareMode, shareId, perQuestionTime, isShared, resumeTest, testStateId, competitionId, mode, isPhrase } = options;

    console.log('📋 词汇测试页面加载参数:', options);

    // 检测是否是从"查看分享页"按钮进入的
    const shareButtonClickTime = wx.getStorageSync('shareButtonClickTime');
    const currentShareId = wx.getStorageSync('currentShareId');

    if (shareButtonClickTime && shareId === currentShareId) {
      const now = Date.now();
      const timeDiff = now - shareButtonClickTime;

      // 如果时间差在合理范围内（1-30秒），认为是从"查看分享页"按钮进入的
      if (timeDiff > 1000 && timeDiff < 30000) {
        console.log('🔄 检测到从"查看分享页"按钮进入，重定向到我的分享页面');

        // 设置重定向标识，隐藏页面内容
        this.setData({
          isRedirecting: true
        });

        // 清除标识
        wx.removeStorageSync('shareButtonClickTime');
        wx.removeStorageSync('currentShareId');

        // 立即重定向
        wx.reLaunch({
          url: '/pages/profile/share/share?from=shareSuccess'
        });
        return;
      }
    }

    // 如果不需要重定向，显示页面内容
    this.setData({
      isRedirecting: false
    });
    
    // 检查是否是测试恢复
    const isTestResume = resumeTest === 'true';
    let restoredState = null;
    
    if (isTestResume && testStateId) {
      console.log('🔄 检测到词汇测试恢复，尝试恢复状态...');
      restoredState = examLock.restoreTestState(testStateId);
    }
    
    // 如果是竞赛模式，加载竞赛数据
    if (shareMode === 'competition' && competitionId) {
      this.loadCompetitionTest(competitionId, options);
      return;
    }
    
    // 如果是分享模式，加载分享测试数据（支持两种参数格式）
    if ((shareMode === 'share' || isShared === 'true') && shareId) {
      this.loadSharedTest(shareId, options);
      return;
    }
    
    // 获取学习数据
    const learningData = app.globalData.learningData;
    if (!learningData || !learningData.words) {
      wx.showToast({ title: '数据加载失败', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 判断是否为练习模式
    const isPracticeMode = shareMode === 'practice';
    
    // 设置每题时间限制
    let questionTimeSeconds = 3; // 默认3秒每题
    let timeLimitDisplay = '';
    
    if (isPracticeMode) {
      timeLimitDisplay = '练习模式';
    } else {
      questionTimeSeconds = parseInt(perQuestionTime) || 15; // 每题时间限制
      timeLimitDisplay = `${questionTimeSeconds}秒/题`;
    }
    
    // 获取分组信息
    const isGrouped = learningData.isGrouped || false;
    const currentGroup = learningData.currentGroup || null;
    const totalGroups = learningData.totalGroups || null;
    const wordsPerGroup = learningData.wordsPerGroup || null;
    
    console.log('分组信息:', {
      isGrouped,
      currentGroup,
      totalGroups,
      wordsPerGroup,
      totalWords: learningData.originalWords ? learningData.originalWords.length : learningData.words.length
    });
    
    this.setData({
      testMode: testMode || 'en_to_cn',
      shareMode: shareMode || 'self',
      shareId: shareId || '',
      perQuestionTime: questionTimeSeconds, // 每题时间限制
      currentQuestionTimeLeft: questionTimeSeconds, // 当前题目剩余时间
      words: learningData.words,
      originalWords: learningData.originalWords || null, // 完整词库，用于生成选项
      totalQuestions: learningData.words.length,
      libraryId: learningData.libraryId,
      libraryName: learningData.libraryName,
      timeDisplay: isPracticeMode ? '练习模式' : `${questionTimeSeconds}秒`,
      timeLimitDisplay: timeLimitDisplay, // 显示用的时间限制文本
      isTestMode: !isPracticeMode, // 练习模式为false，测试模式为true
      isTestResume: isTestResume, // 标记是否为测试恢复
      examMode: isTestResume && restoredState ? true : false, // 如果是测试恢复，直接设置为考试模式
      isPhrase: isPhrase === 'true', // 是否为短语测试模式
      // 分组信息
      isGrouped: isGrouped,
      currentGroup: currentGroup,
      totalGroups: totalGroups,
      wordsPerGroup: wordsPerGroup,
      originalWords: learningData.originalWords || null
    });

    // 设置页面标题
    if (isPhrase === 'true') {
      const title = testMode === 'en_to_cn' ? '短语英译汉' : '短语汉译英';
      wx.setNavigationBarTitle({ title: title });
    }

    // 初始化音频上下文
    this.initAudioContext();

    // 显示准备界面
    if (isPracticeMode) {
      this.showPracticePreparation();
    } else if (!isTestResume) {
      // 只有在非测试恢复时才显示准备界面
      this.showTestPreparation();
    } else {
      // 测试恢复：先恢复状态，再启用锁定模式
      console.log('🔄 词汇测试恢复：恢复状态并启用锁定模式');
      if (restoredState) {
        this.restoreTestState(restoredState);
      } else {
        // 没有恢复状态，直接开始新测试
        console.log('⚠️ 没有找到恢复状态，开始新测试');
        this.showTestPreparation();
      }
    }
  },

  // 加载竞赛测试数据
  async loadCompetitionTest(competitionId, options) {
    wx.showLoading({ title: '加载竞赛数据...' });
    
    try {
      // 调用云函数获取竞赛详情
      const result = await wx.cloud.callFunction({
        name: 'getCompetitionDetail',
        data: {
          competitionId: competitionId
        }
      });

      if (!result.result.success) {
        wx.hideLoading();
        wx.showModal({
          title: '加载失败',
          content: result.result.message || '竞赛不存在或已过期',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }

      const competitionData = result.result.data;
      const { mode } = options;
      
      // 根据竞赛模式设置测试模式
      let actualTestMode = 'en_to_cn';
      if (mode === 'en2zh') {
        actualTestMode = 'en_to_cn';
      } else if (mode === 'zh2en') {
        actualTestMode = 'cn_to_en';
      }
      
      // 设置测试数据
      this.setData({
        testMode: actualTestMode,
        shareMode: 'competition',
        competitionId: competitionId,
        competitionName: competitionData.name,
        masterCompetitionId: options.masterCompetitionId || null, // 主竞赛ID（多关卡模式）
        perQuestionTime: competitionData.timeLimit || 10, // 竞赛时间限制
        currentQuestionTimeLeft: competitionData.timeLimit || 10,
        words: competitionData.words,
        totalQuestions: competitionData.words.length,
        libraryId: competitionData.libraryId,
        libraryName: competitionData.libraryName,
        timeDisplay: `${competitionData.timeLimit || 10}秒`,
        timeLimitDisplay: `${competitionData.timeLimit || 10}秒/题`,
        isTestMode: true, // 竞赛默认为测试模式
        isCompetition: true, // 标识为竞赛模式
        hasNextLevel: false, // 是否有下一关
        nextLevelId: null, // 下一关ID
        nextLevelNumber: null, // 下一关编号
        competitionCompleted: false // 当前关卡是否完成
      });
      
      wx.hideLoading();
      
      // 初始化音频上下文
      this.initAudioContext();
      
      // 显示竞赛准备界面
      this.showCompetitionPreparation();
      
    } catch (error) {
      wx.hideLoading();
      console.error('加载竞赛失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载分享测试数据
  loadSharedTest: async function(shareId, options) {
    wx.showLoading({ title: '加载分享测试...' });
    
    try {
      let shareTestData = null;
      
      // 首先尝试从云端数据库获取分享测试数据
      console.log('=== 开始加载分享测试 ===');
      console.log('分享ID:', shareId);

      try {
        console.log('调用 getShareTest 云函数...');
        const result = await wx.cloud.callFunction({
          name: 'getShareTest',
          data: { shareId: shareId }
        });

        console.log('云函数调用结果:', result);

        if (result.result && result.result.success) {
          shareTestData = result.result.data;
          console.log('从云端加载分享测试成功:', {
            shareId: shareTestData?.shareId,
            testMode: shareTestData?.testMode,
            wordsCount: shareTestData?.words?.length,
            hasWordSelection: !!shareTestData?.wordSelection,
            storageStrategy: shareTestData?.storageStrategy
          });
        } else {
          console.error('云函数返回失败:', {
            success: result.result?.success,
            message: result.result?.message,
            fullResult: result.result
          });
          
          // 检查是否过期
          if (shareTestData.expireTime) {
            const now = new Date();
            const expireTime = new Date(shareTestData.expireTime);
            if (now > expireTime) {
              console.log('分享测试已过期:', expireTime);
              shareTestData = null;
            }
          }
        }
      } catch (cloudError) {
        console.error('从云端加载分享测试失败:', cloudError);
        console.error('错误详情:', {
          errMsg: cloudError.errMsg,
          errCode: cloudError.errCode,
          message: cloudError.message
        });
        // 不再尝试本地存储，因为分享测试必须在云端
      }

      // 检查分享测试数据是否存在
      if (!shareTestData) {
        wx.hideLoading();
        wx.showModal({
          title: '分享测试不存在',
          content: '该分享测试不存在或已过期。可能的原因：\n1. 分享链接错误\n2. 测试已过期（7天有效期）\n3. 创建者未成功上传到云端\n\n请联系分享者重新创建分享测试。',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }

      // 检查词汇数据（支持索引存储策略）
      if (!shareTestData.words && !shareTestData.wordSelection) {
        wx.hideLoading();
        wx.showModal({
          title: '测试数据异常',
          content: '分享测试的词汇数据缺失，请重新获取分享链接。',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      // 记录被分享人信息
      this.recordSharedTestAccess(shareId, shareTestData);
      
      // 获取当前用户信息
      const currentUser = wx.getStorageSync('userInfo') || {};
      
      // 处理多关卡任务
      let currentLevelId = 1;
      let currentWords = shareTestData.words;
      let levelProgress = null;
      
      if (shareTestData.isMultiLevel) {
        // 获取当前用户的关卡进度
        levelProgress = shareTestData.levelProgress && shareTestData.levelProgress[currentUser.openid];
        
        if (levelProgress) {
          currentLevelId = levelProgress.currentLevel || 1;
        }
        
        // 获取当前关卡的单词
        const currentLevel = shareTestData.levels.find(level => level.levelId === currentLevelId);
        if (currentLevel) {
          currentWords = currentLevel.words;
        } else if (shareTestData.levels.length === 0) {
          // 如果levels为空，说明使用延迟生成策略，需要动态加载关卡数据
          console.log('检测到延迟生成关卡，开始动态加载关卡数据');
          // 保存options供后续使用
          this.setData({ options: options });
          await this.loadLevelData(shareId, currentLevelId);
          return; // 加载完成后会重新调用loadSharedTest
        }
        
        console.log('多关卡任务:', {
          totalLevels: shareTestData.totalLevels,
          currentLevelId: currentLevelId,
          currentWords: currentWords.length,
          levelProgress: levelProgress
        });
      }
      
      // 设置测试数据
      const { testMode, timeLimit, perQuestionTime, levelId } = options;
      
      // 如果通过参数指定了关卡ID，使用指定的关卡
      if (levelId && shareTestData.isMultiLevel) {
        const specifiedLevel = shareTestData.levels.find(level => level.levelId === parseInt(levelId));
        if (specifiedLevel) {
          currentLevelId = parseInt(levelId);
          currentWords = specifiedLevel.words;
        }
      }
      
      const actualTestMode = testMode || shareTestData.testType || 'en_to_cn';
      
      // 判断是否为练习模式（支持多种字段）
      const isPracticeMode = shareTestData.practiceMode === 'practice' || shareTestData.mode === 'practice';
      
      // 设置每题时间限制
      let questionTimeSeconds = 15; // 默认15秒每题
      let timeLimitDisplay = '';

      if (isPracticeMode) {
        timeLimitDisplay = '练习模式';
      } else {
        // 优先级：分享设置 > URL参数 > 默认值
        if (shareTestData.settings) {
          questionTimeSeconds = parseInt(shareTestData.settings.timeLimit) ||
                               parseInt(shareTestData.settings.perQuestionTime) ||
                               parseInt(perQuestionTime) || 15;
        } else {
          questionTimeSeconds = parseInt(perQuestionTime) || 15;
        }
        timeLimitDisplay = `${questionTimeSeconds}秒/题`;
      }

      // 获取分享测试的分组设置
      const shareWordsPerGroup = shareTestData.wordsPerGroup ||
                                (shareTestData.settings && shareTestData.settings.wordsPerGroup) ||
                                null;
      const isGroupedShare = shareWordsPerGroup && shareWordsPerGroup > 0;

      console.log('分享测试分组设置:', {
        wordsPerGroup: shareWordsPerGroup,
        isGrouped: isGroupedShare,
        totalWords: currentWords.length
      });

      this.setData({
        testMode: actualTestMode,
        shareMode: 'share',
        shareId: shareId,
        perQuestionTime: questionTimeSeconds,
        currentQuestionTimeLeft: questionTimeSeconds,
        words: currentWords,
        totalQuestions: currentWords.length,
        libraryId: shareTestData.libraryId || 'shared',
        libraryName: shareTestData.libraryName || '分享测试',
        timeDisplay: isPracticeMode ? '练习模式' : `${questionTimeSeconds}秒`,
        timeLimitDisplay: timeLimitDisplay,
        isTestMode: !isPracticeMode,
        // 分组信息
        isGrouped: isGroupedShare,
        wordsPerGroup: shareWordsPerGroup,
        currentGroup: isGroupedShare ? 1 : null,
        totalGroups: isGroupedShare ? Math.ceil(currentWords.length / shareWordsPerGroup) : null,
        // 多关卡信息
        isMultiLevel: shareTestData.isMultiLevel || false,
        currentLevelId: currentLevelId,
        totalLevels: shareTestData.totalLevels || 1,
        levelProgress: levelProgress,
        allLevels: shareTestData.levels || [],
        shareTestData: shareTestData // 保存完整的分享测试数据
      });
      
      wx.hideLoading();
      
      // 初始化音频上下文
      this.initAudioContext();
      
      // 显示准备界面
      if (isPracticeMode) {
        this.showPracticePreparation();
      } else {
        this.showTestPreparation();
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('加载分享测试失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 记录分享测试访问
  recordSharedTestAccess: function(shareId, shareTestData) {
    try {
      const currentUser = wx.getStorageSync('userInfo') || {};
      const currentTime = Date.now();
      
      // 检查是否已记录过该用户的访问
      if (!shareTestData.visitors) {
        shareTestData.visitors = [];
      }
      
      const existingVisitor = shareTestData.visitors.find(visitor => 
        visitor.openid === currentUser.openid
      );
      
      if (!existingVisitor && currentUser.openid) {
        // 添加新访问者记录
        shareTestData.visitors.push({
          openid: currentUser.openid,
          nickName: currentUser.nickName || '匿名用户',
          avatar: currentUser.avatarUrl || '',
          firstVisitTime: currentTime,
          lastVisitTime: currentTime,
          visitCount: 1,
          testCount: 0, // 测试次数，完成测试后会更新
          bestScore: 0, // 最高分数
          remark: '' // 备注，可由分享者添加
        });
        
        // 更新存储（只更新访问者信息，不保存完整词汇数据）
        try {
          const shareTests = wx.getStorageSync('shareTests') || {};
          if (shareTests[shareId]) {
            // 只更新访问者信息
            shareTests[shareId].visitors = shareTestData.visitors;
            wx.setStorageSync('shareTests', shareTests);
          }
        } catch (error) {
          console.error('更新本地存储访问者信息失败:', error);
        }
        
        console.log('记录新访问者:', currentUser.nickName);
      } else if (existingVisitor) {
        // 更新已有访问者的访问时间和次数
        existingVisitor.lastVisitTime = currentTime;
        existingVisitor.visitCount = (existingVisitor.visitCount || 1) + 1;
        
        // 更新存储（只更新访问者信息，不保存完整词汇数据）
        try {
          const shareTests = wx.getStorageSync('shareTests') || {};
          if (shareTests[shareId]) {
            // 只更新访问者信息
            shareTests[shareId].visitors = shareTestData.visitors;
            wx.setStorageSync('shareTests', shareTests);
          }
        } catch (error) {
          console.error('更新本地存储访问者信息失败:', error);
        }
        
        console.log('更新访问者记录:', existingVisitor.nickName);
      }
    } catch (error) {
      console.error('记录分享测试访问失败:', error);
    }
  },

  onUnload: function() {
    // 清理定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    // 清理每题计时器
    if (this.data.questionTimer) {
      clearInterval(this.data.questionTimer);
    }
    // 安全清理音频上下文
    try {
      const audioContext = this.data.audioContext;
      if (audioContext) {
        // 检查是否有destroy方法
        if (typeof audioContext.destroy === 'function') {
          audioContext.destroy();
          console.log('音频上下文已安全销毁');
        } else if (typeof audioContext.stop === 'function') {
          // 如果没有destroy方法，尝试停止播放
          audioContext.stop();
          console.log('音频播放已停止');
        }
      }
    } catch (error) {
      console.error('销毁音频上下文失败:', error);
    }
  },

  // 显示测试准备界面
  showTestPreparation: function() {
    const { testMode, timeLimitDisplay, totalQuestions, libraryName, perQuestionTime, isGrouped, currentGroup, totalGroups, isMultiLevel, currentLevelId, totalLevels, levelProgress, isPhrase } = this.data;
    const modeText = testMode === 'en_to_cn' ? (isPhrase ? '短语英译汉' : '英译汉') : (isPhrase ? '短语汉译英' : '汉译英');

    // 构建内容文本
    let content = `词库：${libraryName}\n模式：${modeText}`;
    
    // 如果是分组测试，显示分组信息
    if (isGrouped && currentGroup && totalGroups) {
      content += `\n当前：第${currentGroup}组 / 共${totalGroups}组`;
    }
    
    // 如果是多关卡任务，显示关卡信息
    if (isMultiLevel) {
      content += `\n关卡：第${currentLevelId}关 / 共${totalLevels}关`;
      if (levelProgress && levelProgress.completedLevels) {
        content += `\n已完成：${levelProgress.completedLevels.length}关`;
      }
    }
    
    content += `\n题目数量：${totalQuestions}题\n时间限制：每题${perQuestionTime}秒\n\n⚠️ 重要提示：\n• 每道题限时${perQuestionTime}秒\n• 超时未答按错误处理\n• 时间到自动进入下一题`;
    
    // 如果是多关卡任务，添加关卡相关提示
    if (isMultiLevel) {
      content += `\n• 通过当前关卡将解锁下一关\n• 需要达到80分以上才能通过`;
    }
    
    content += `\n\n测试期间将启用防作弊模式：\n• 全屏专注模式\n• 无法中途退出\n• 屏蔽返回按键\n• 保持屏幕常亮\n\n确定要开始测试吗？`;
    
    wx.showModal({
      title: isPhrase ? '🔒 短语测试模式' : '🔒 词汇测试模式',
      content: content,
      confirmText: '开始测试',
      cancelText: '返回',
      confirmColor: '#007aff',
      success: (res) => {
        if (res.confirm) {
          this.enableExamMode(); // 先启用考试模式
        } else {
          wx.navigateBack();
        }
      }
    });
  },

  // 显示练习模式准备界面
  showPracticePreparation: function() {
    const { testMode, totalQuestions, libraryName, isPhrase } = this.data;
    const modeText = testMode === 'en_to_cn' ? (isPhrase ? '短语英译汉' : '英译汉') : (isPhrase ? '短语汉译英' : '汉译英');
    
    wx.showModal({
      title: '练习准备',
      content: `词库：${libraryName}\n模式：${modeText}练习\n${isPhrase ? '短语' : '词汇'}数量：${totalQuestions}个\n\n将显示完整${isPhrase ? '短语' : '单词'}信息（包含音标、释义、例句），您可以按自己的节奏学习。\n\n准备开始练习吗？`,
      confirmText: '开始练习',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm) {
          this.startPractice();
        } else {
          wx.navigateBack();
        }
      }
    });
  },

  // 显示竞赛准备界面
  showCompetitionPreparation: function() {
    const { testMode, totalQuestions, libraryName, competitionName, perQuestionTime } = this.data;
    const modeText = testMode === 'en_to_cn' ? '英译汉' : '汉译英';
    
    wx.showModal({
      title: '🏆 竞赛挑战',
      content: `竞赛名称：${competitionName}\n词库：${libraryName}\n模式：${modeText}\n题目数量：${totalQuestions}题\n时间限制：每题${perQuestionTime}秒\n\n⚠️ 竞赛规则：\n• 每道题限时${perQuestionTime}秒\n• 超时未答按错误处理\n• 时间到自动进入下一题\n• 完成后自动提交成绩\n\n测试期间将启用专注模式：\n• 全屏专注模式\n• 无法中途退出\n• 屏蔽返回按键\n• 保持屏幕常亮\n\n准备好挑战了吗？`,
      confirmText: '开始挑战',
      cancelText: '返回',
      confirmColor: '#007aff',
      success: (res) => {
        if (res.confirm) {
          this.enableExamMode(); // 启用考试模式开始竞赛
        } else {
          wx.navigateBack();
        }
      }
    });
  },

  // 开始测试
  startTest: function() {
    const now = Date.now();
    this.setData({
      testStarted: true,
      startTime: now
    });

    console.log('🚀 词汇测试开始，记录开始时间:', new Date(now).toLocaleString());

    // 开始第一题（会启动每题计时器）
    this.nextQuestion();
  },

  // 启用考试模式
  enableExamMode: function() {
    const examName = this.data.isPhrase ? '短语测试' : '词汇测试';
    console.log(`启用${examName}考试模式`);

    // 启用完整的防作弊锁定功能
    examLock.enable({
      examName: examName,
      onBackAttempt: (attemptCount) => {
        // 用户尝试退出时的回调
        console.log(`用户第${attemptCount}次尝试退出${examName}`);

        // 不暂停计时器，让测试继续进行
        console.log('⏰ 计时器继续运行，测试正常进行');

        wx.showToast({
          title: `检测到退出尝试(${attemptCount}/3)`,
          icon: 'none',
          duration: 1500
        });
      },
      onExitConfirm: () => {
        // 用户确认退出时的回调
        console.log(`用户确认强制退出${examName}，测试将继续在后台进行`);

        // 记录测试被中断
        this.recordTestInterruption();

        // 显示退出提示，说明测试继续进行
        wx.showToast({
          title: '测试继续后台进行，将自动提交成绩',
          icon: 'none',
          duration: 3000
        });

        // 延迟执行退出逻辑
        setTimeout(() => {
          wx.navigateBack({
            delta: 1,
            fail: () => {
              wx.reLaunch({
                url: '/pages/wordtest/mode-select/mode-select',
                fail: () => {
                  wx.switchTab({
                    url: '/pages/index/index'
                  });
                }
              });
            }
          });
        }, 300);
      }
    });
    
    // 设置考试状态
    this.setData({
      examMode: true
    });
    
    console.log('词汇测试锁定模式已启用');
    
    // 启动测试
    this.startTest();
  },

  // 恢复测试状态
  restoreTestState: function(restoredState) {
    console.log('🔄 恢复词汇测试状态:', restoredState);
    console.log('🔍 状态验证详情:', {
      hasStartTime: !!restoredState.startTime,
      startTime: restoredState.startTime,
      hasCurrentQuestion: restoredState.currentQuestion !== undefined,
      currentQuestion: restoredState.currentQuestion,
      hasCurrentIndex: restoredState.currentIndex !== undefined,
      currentIndex: restoredState.currentIndex,
      hasTotalQuestions: restoredState.totalQuestions !== undefined,
      totalQuestions: restoredState.totalQuestions,
      hasWords: !!restoredState.words,
      wordsLength: restoredState.words?.length || 0,
      testMode: restoredState.testMode,
      shareMode: restoredState.shareMode
    });

    // 检查是否有有效的测试数据
    const hasValidTestData = restoredState.startTime &&
                            (restoredState.currentQuestion !== undefined || restoredState.currentIndex !== undefined) &&
                            restoredState.totalQuestions !== undefined &&
                            restoredState.words && restoredState.words.length > 0;

    if (hasValidTestData) {
      // 恢复测试数据
      let currentIndex;
      if (restoredState.currentIndex !== undefined) {
        currentIndex = restoredState.currentIndex;
      } else if (restoredState.currentQuestion !== undefined) {
        currentIndex = (restoredState.currentQuestion || 1) - 1; // 转换为0基索引
      } else {
        currentIndex = 0;
      }

      console.log('🔄 恢复测试数据详情:', {
        currentIndex,
        totalQuestions: restoredState.totalQuestions,
        words: restoredState.words?.length || 0,
        testMode: restoredState.testMode,
        firstWord: restoredState.words?.[0],
        currentWordFromState: restoredState.words?.[currentIndex]
      });

      this.setData({
        testStarted: true, // 如果有测试数据，说明测试已开始
        startTime: restoredState.startTime,
        currentIndex: currentIndex,
        correctCount: restoredState.correctCount || 0,
        wrongCount: restoredState.wrongCount || 0,
        userAnswers: restoredState.userAnswers || [],
        mistakes: restoredState.mistakes || [],
        questionStartTime: restoredState.questionStartTime,
        currentQuestionTimeLeft: restoredState.currentQuestionTimeLeft || this.data.perQuestionTime,
        examMode: true,
        // 恢复测试配置
        words: restoredState.words || this.data.words,
        totalQuestions: restoredState.totalQuestions || this.data.totalQuestions,
        testMode: restoredState.testMode || this.data.testMode,
        isTestMode: restoredState.isTestMode !== undefined ? restoredState.isTestMode : this.data.isTestMode,
        shareMode: restoredState.shareMode || this.data.shareMode,
        perQuestionTime: restoredState.perQuestionTime || this.data.perQuestionTime,
        // 重置答题状态，确保可以正常答题
        isAnswered: false,
        showAnswer: false,
        selectedOption: -1,
        correctOption: -1,
        userInput: '',
        isCorrect: false
      });

      console.log('🔄 测试已开始，直接恢复测试状态');
      console.log('🔄 恢复的数据:', {
        startTime: restoredState.startTime,
        currentIndex: currentIndex,
        currentQuestion: restoredState.currentQuestion,
        totalQuestions: restoredState.totalQuestions
      });

      // 加载当前题目
      this.loadCurrentWord();

      // 启动考试模式并恢复时间计算
      this.resumeExamMode();
    } else {
      console.log('🔄 没有有效的测试数据，显示准备界面');
      this.showTestPreparation();
    }
  },

  // 恢复考试模式（测试中断后恢复）
  resumeExamMode: function() {
    const examName = this.data.isPhrase ? '短语测试' : '词汇测试';
    console.log(`🔄 恢复${examName}考试模式`);

    // 直接启用完整防作弊锁定，不显示确认对话框
    examLock.enable({
      examName: examName,
      onBackAttempt: (attemptCount) => {
        console.log(`用户第${attemptCount}次尝试退出${examName}`);

        // 不暂停计时器，让测试继续进行
        console.log('⏰ 计时器继续运行，测试正常进行');
      },
      onExitConfirm: () => {
        console.log(`用户确认强制退出${examName}，测试将继续在后台进行`);
        this.recordTestInterruption();

        wx.showToast({
          title: '测试继续后台进行，将自动提交成绩',
          icon: 'none',
          duration: 3000
        });

        setTimeout(() => {
          wx.navigateBack({
            delta: 1,
            fail: () => {
              wx.reLaunch({
                url: '/pages/wordtest/mode-select/mode-select',
                fail: () => {
                  wx.switchTab({
                    url: '/pages/index/index'
                  });
                }
              });
            }
          });
        }, 300);
      }
    });

    // 设置考试状态
    this.setData({
      examMode: true
    });

    // 恢复测试状态，计算已经过去的时间
    this.resumeTestWithTimeCalculation();
  },

  // 恢复测试并计算时间
  resumeTestWithTimeCalculation: function() {
    const now = Date.now();
    const { startTime, questionStartTime, perQuestionTime, currentIndex, totalQuestions } = this.data;

    if (startTime) {
      // 计算测试总共已经过去的时间
      const totalElapsedSeconds = Math.floor((now - startTime) / 1000);
      const totalTestTime = totalQuestions * perQuestionTime;

      console.log(`🕐 恢复测试时间计算:`, {
        totalElapsedSeconds,
        totalTestTime,
        currentIndex,
        totalQuestions
      });

      // 检查测试是否已经结束
      if (totalElapsedSeconds >= totalTestTime) {
        // 测试时间已到，直接结束测试
        wx.showModal({
          title: '测试已结束',
          content: '测试时间已到，系统已自动提交您的答案。',
          showCancel: false,
          confirmText: '查看结果',
          success: () => {
            this.endTest();
          }
        });
        return;
      }

      // 计算应该在第几题
      const expectedQuestionIndex = Math.floor(totalElapsedSeconds / perQuestionTime);
      const actualQuestionIndex = Math.min(expectedQuestionIndex, totalQuestions - 1);

      // 计算当前题目的剩余时间
      const questionElapsedSeconds = totalElapsedSeconds % perQuestionTime;
      const questionTimeLeft = Math.max(0, perQuestionTime - questionElapsedSeconds);

      console.log(`📊 题目跳转计算:`, {
        expectedQuestionIndex,
        actualQuestionIndex,
        currentIndex,
        questionElapsedSeconds,
        questionTimeLeft
      });

      // 如果需要跳转题目
      if (actualQuestionIndex > currentIndex) {
        // 标记跳过的题目为错误
        for (let i = currentIndex; i < actualQuestionIndex; i++) {
          this.data.userAnswers[i] = {
            answer: '',
            isCorrect: false,
            isTimeout: true,
            isSkipped: true, // 标记为因退出而跳过
            word: this.data.words[i]
          };
          this.data.wrongCount++;

          // 添加到错误词汇列表
          if (this.data.words[i]) {
            this.data.mistakes.push({
              word: this.data.words[i],
              userAnswer: '',
              correctAnswer: this.data.words[i].words || this.data.words[i].word,
              isSkipped: true
            });
          }
        }

        // 跳转到正确的题目
        this.setData({
          currentIndex: actualQuestionIndex,
          wrongCount: this.data.wrongCount,
          mistakes: this.data.mistakes
        });

        // 加载新题目
        this.loadCurrentWord();

        wx.showToast({
          title: `已跳转到第${actualQuestionIndex + 1}题，跳过${actualQuestionIndex - currentIndex}题`,
          icon: 'none',
          duration: 2000
        });
      }

      // 检查当前题目是否还有时间
      if (questionTimeLeft > 0 && actualQuestionIndex < totalQuestions) {
        // 还有时间，恢复计时器
        this.startQuestionTimer(questionTimeLeft);

        wx.showToast({
          title: `测试已恢复，本题剩余${questionTimeLeft}秒`,
          icon: 'success',
          duration: 2000
        });
      } else if (actualQuestionIndex < totalQuestions) {
        // 当前题目时间已到，自动处理超时
        wx.showToast({
          title: '当前题目时间已到，自动跳转',
          icon: 'none',
          duration: 1500
        });

        setTimeout(() => {
          this.handleQuestionTimeout();
        }, 1500);
      } else {
        // 所有题目都已完成，结束测试
        this.endTest();
      }
    } else {
      // 没有保存的时间信息，重新开始当前题目
      this.startQuestionTimer();

      wx.showToast({
        title: '测试已恢复',
        icon: 'success',
        duration: 2000
      });
    }
  },

  // 禁用考试模式
  disableExamMode: function() {
    const examName = this.data.isPhrase ? '短语测试' : '词汇测试';
    console.log(`禁用${examName}考试模式`);

    // 禁用防作弊锁定功能
    examLock.disable();

    // 清除考试状态
    this.setData({
      examMode: false
    });

    console.log(`${examName}锁定模式已禁用`);
  },

  // 记录测试中断
  recordTestInterruption: function(skipExamLockSave = false) {
    const now = Date.now();
    const interruptionData = {
      timestamp: now,
      testType: 'vocabulary',
      testMode: this.data.testMode,
      stage: '测试进行中',
      currentQuestion: this.data.currentIndex + 1,
      totalQuestions: this.data.totalQuestions,
      correctCount: this.data.correctCount,
      wrongCount: this.data.wrongCount,
      timeLeft: this.data.timeLeft,
      // 保存时间相关信息，用于恢复时计算
      startTime: this.data.startTime,
      questionStartTime: this.data.questionStartTime,
      currentQuestionTimeLeft: this.data.currentQuestionTimeLeft,
      perQuestionTime: this.data.perQuestionTime,
      // 保存测试数据，用于恢复
      words: this.data.words,
      currentWord: this.data.currentWord,
      isTestMode: this.data.isTestMode,
      shareMode: this.data.shareMode,
      mistakes: this.data.mistakes,
      userAnswers: this.data.userAnswers || [],
      // 保存答题状态
      isAnswered: this.data.isAnswered,
      selectedOption: this.data.selectedOption,
      correctOption: this.data.correctOption,
      options: this.data.options,
      userInput: this.data.userInput
    };

    console.log('记录词汇测试中断:', interruptionData);

    // 只有在不是被examLock调用时才保存到examLock（避免重复保存）
    if (!skipExamLockSave && examLock && typeof examLock.saveCurrentTestState === 'function') {
      examLock.saveCurrentTestState(interruptionData);
    }

    // 保存中断数据到本地存储
    try {
      const interruptions = wx.getStorageSync('test_interruptions') || [];
      interruptions.push(interruptionData);
      wx.setStorageSync('test_interruptions', interruptions);
    } catch (error) {
      console.error('保存中断记录失败:', error);
    }

    return interruptionData;
  },

  // 开始练习
  startPractice: function() {
    this.setData({
      testStarted: true,
      startTime: Date.now()
    });
    
    // 练习模式不启动计时器
    // 开始第一个词汇
    this.nextPracticeWord();
  },

  // 启动每题计时器
  startQuestionTimer: function(remainingTime = null) {
    // 清理之前的计时器
    if (this.data.questionTimer) {
      clearInterval(this.data.questionTimer);
    }

    // 设置当前题目时间（如果有剩余时间则使用剩余时间，否则使用完整时间）
    const { perQuestionTime } = this.data;
    const initialTime = remainingTime !== null ? remainingTime : perQuestionTime;

    // 记录题目开始时间（用于后台时间计算）
    const questionStartTime = Date.now() - (perQuestionTime - initialTime) * 1000;

    this.setData({
      currentQuestionTimeLeft: initialTime,
      timeDisplay: `${initialTime}秒`,
      questionStartTime: questionStartTime // 保存题目开始的绝对时间
    });

    // 启动每题计时器
    this.data.questionTimer = setInterval(() => {
      // 基于绝对时间计算剩余时间，确保后台时间继续
      const now = Date.now();
      const elapsedSeconds = Math.floor((now - this.data.questionStartTime) / 1000);
      const timeLeft = Math.max(0, perQuestionTime - elapsedSeconds);

      this.setData({
        currentQuestionTimeLeft: timeLeft,
        timeDisplay: `${timeLeft}秒`
      });

      // 时间到，自动跳转到下一题
      if (timeLeft <= 0) {
        clearInterval(this.data.questionTimer);
        this.handleQuestionTimeout();
      }
    }, 1000);
  },

  // 处理题目超时
  handleQuestionTimeout: function() {
    if (this.data.isAnswered) return; // 如果已经答过了，不处理超时

    // 对于单词汉译英填空题，检查是否有用户输入
    if (this.data.testMode === 'cn_to_en' && !this.data.isPhrase && this.data.userInput && this.data.userInput.trim()) {
      // 有输入内容，按照输入内容判断正误
      this.onSubmitAnswer();
      return;
    }

    // 没有输入或者是选择题模式，记录为超时错题
    const mistakeData = {
      word: this.data.currentWord,
      selectedAnswer: (this.data.testMode === 'cn_to_en' && !this.data.isPhrase) ? '超时未答' : '超时未选',
      correctAnswer: (this.data.testMode === 'en_to_cn' || (this.data.testMode === 'cn_to_en' && this.data.isPhrase))
        ? this.data.options[this.data.correctOption]
        : (this.data.currentWord.words || this.data.currentWord.word),
      timestamp: Date.now()
    };

    this.data.mistakes.push(mistakeData);

    // 在测试模式下，自动添加错题到错题本（包括分享测试）
    console.log('错词收集检查:', {
      isTestMode: this.data.isTestMode,
      shareMode: this.data.shareMode,
      shouldCollect: this.data.isTestMode
    });

    if (this.data.isTestMode) {
      console.log('开始收集错词到错题本:', mistakeData);
      this.addToMistakeBook(mistakeData);
    }

    this.setData({
      isAnswered: true,
      showAnswer: true,
      selectedOption: -1, // 没有选择任何选项
      isCorrect: false, // 超时为错误
      wrongCount: this.data.wrongCount + 1
    });

    // 自动进入下一题
    setTimeout(() => {
      this.setData({
        currentIndex: this.data.currentIndex + 1,
        userInput: '', // 重置填空题输入
        showAnswer: false,
        isAnswered: false,
        isCorrect: false,
        inputFocused: false
      });
      this.nextQuestion();
    }, 1500);
  },

  // 加载当前题目
  loadCurrentWord: function() {
    console.log('🔍 loadCurrentWord 调试信息:', {
      currentIndex: this.data.currentIndex,
      wordsLength: this.data.words?.length || 0,
      totalQuestions: this.data.totalQuestions,
      hasWords: !!this.data.words
    });

    if (this.data.currentIndex >= this.data.words.length) {
      this.endTest();
      return;
    }

    const currentWord = this.data.words[this.data.currentIndex];
    console.log('🔍 当前词汇对象:', currentWord);
    const progressPercent = ((this.data.currentIndex + 1) / this.data.totalQuestions * 100);

    console.log('🔄 加载当前题目:', {
      currentIndex: this.data.currentIndex,
      totalQuestions: this.data.totalQuestions,
      word: currentWord?.words || currentWord?.word || currentWord?.english,
      testMode: this.data.testMode
    });

    this.setData({
      currentWord: currentWord,
      isAnswered: false,
      showAnswer: false,
      selectedOption: -1,
      correctOption: -1,
      progressPercent: progressPercent,
      userInput: '', // 重置填空题输入
      isCorrect: false // 重置正确性标志
    });

    // 英译汉模式或短语汉译英模式都生成选项
    if (this.data.testMode === 'en_to_cn' || (this.data.testMode === 'cn_to_en' && this.data.isPhrase)) {
      this.generateOptions(currentWord);
    }

    // 单词汉译英模式自动聚焦输入框（短语汉译英不需要）
    if (this.data.testMode === 'cn_to_en' && !this.data.isPhrase) {
      setTimeout(() => {
        this.setData({
          inputFocused: true
        });
      }, 100);
    }

    // 在测试模式下启动每题计时器
    if (this.data.isTestMode) {
      this.startQuestionTimer();
    }
  },

  // 下一题
  nextQuestion: function() {
    this.loadCurrentWord();
  },

  // 生成选项（用于英译汉模式和短语汉译英模式）
  generateOptions: function(currentWord) {
    const { testMode, words, originalWords, isPhrase } = this.data;

    // 选择数据源：优先使用整个词库(originalWords)，如果没有则使用当前单词列表(words)
    const dataSource = originalWords || words;

    console.log('🎯 生成选项:', {
      testMode,
      isPhrase,
      currentWord: currentWord?.words || currentWord?.word || currentWord?.english,
      meaning: currentWord?.meaning,
      dataSourceSize: dataSource?.length || 0,
      usingOriginalWords: !!originalWords
    });

    // 英译汉模式或短语汉译英模式才生成选项
    if (testMode !== 'en_to_cn' && !(testMode === 'cn_to_en' && isPhrase)) {
      console.log('⚠️ 不支持的模式，跳过选项生成');
      return;
    }

    let result = {};

    if (testMode === 'en_to_cn') {
      // 英译汉：显示英文，选择中文
      result = optionGenerator.generateWordOptions(dataSource, 'en_to_cn', currentWord, 4);
    } else if (testMode === 'cn_to_en' && isPhrase) {
      // 短语汉译英：显示中文，选择英文
      const correctAnswer = currentWord.words || currentWord.word;
      const options = optionGenerator.generatePhraseOptions(dataSource, 'words', correctAnswer, 4);
      const correctIndex = options.indexOf(correctAnswer);
      result = { options, correctIndex };
    }

    console.log('🎯 选项生成完成:', {
      options: result.options,
      correctIndex: result.correctIndex
    });

    this.setData({
      options: result.options || [],
      correctOption: result.correctIndex >= 0 ? result.correctIndex : 0
    });
  },



  // 选择答案
  onOptionTap: function(e) {
    console.log('🔘 用户点击选项:', {
      isAnswered: this.data.isAnswered,
      testMode: this.data.testMode,
      currentWord: this.data.currentWord?.word || this.data.currentWord?.english,
      options: this.data.options,
      correctOption: this.data.correctOption
    });

    if (this.data.isAnswered) {
      console.log('⚠️ 已经回答过了，忽略点击');
      return;
    }

    // 停止每题计时器
    if (this.data.questionTimer) {
      clearInterval(this.data.questionTimer);
    }

    const selectedIndex = e.currentTarget.dataset.index;
    const isCorrect = selectedIndex === this.data.correctOption;

    console.log('🔘 选项点击详情:', {
      selectedIndex,
      correctOption: this.data.correctOption,
      isCorrect,
      selectedAnswer: this.data.options[selectedIndex],
      correctAnswer: this.data.options[this.data.correctOption]
    });

    // 记录错题
    if (!isCorrect) {
      const mistakeData = {
        word: this.data.currentWord,
        selectedAnswer: this.data.options[selectedIndex],
        correctAnswer: this.data.options[this.data.correctOption],
        timestamp: Date.now()
      };
      
      this.data.mistakes.push(mistakeData);
      
      // 在测试模式下，自动添加错题到错题本（包括分享测试）
      console.log('错词收集检查:', {
        isTestMode: this.data.isTestMode,
        shareMode: this.data.shareMode,
        shouldCollect: this.data.isTestMode
      });

      if (this.data.isTestMode) {
        console.log('开始收集错词到错题本:', mistakeData);
        this.addToMistakeBook(mistakeData);
      }
    }

    this.setData({
      selectedOption: selectedIndex,
      isAnswered: true,
      showAnswer: true,
      correctCount: this.data.correctCount + (isCorrect ? 1 : 0),
      wrongCount: this.data.wrongCount + (isCorrect ? 0 : 1),
      score: this.data.score + (isCorrect ? 10 : 0)
    });

    // 添加反馈效果
    if (isCorrect) {
      // 正确答案的反馈
      wx.vibrateShort({ type: 'light' });
      this.playCorrectSound();

      // 只在非英译汉模式下显示正确弹窗
      if (this.data.testMode !== 'en_to_cn') {
        wx.showToast({
          title: '✓ 正确',
          icon: 'success',
          duration: 800
        });
      }
    } else {
      // 错误答案的反馈
      wx.vibrateShort({ type: 'heavy' });
      this.playWrongSound();
      wx.showToast({
        title: '✗ 错误',
        icon: 'error',
        duration: 800
      });
    }

    // 自动进入下一题
    setTimeout(() => {
      this.setData({
        currentIndex: this.data.currentIndex + 1
      });
      this.nextQuestion();
    }, 1500);
  },

  // 处理填空题输入
  onInputChange: function(e) {
    const inputValue = e.detail.value;
    console.log('用户输入变化:', inputValue);
    this.setData({
      userInput: inputValue
    });
  },

  // 处理填空题确认输入
  onInputConfirm: function(e) {
    if (this.data.userInput.trim() && !this.data.isAnswered) {
      this.onSubmitAnswer();
    }
  },

  // 验证答案是否正确（支持特殊格式）
  validateAnswer: function(userAnswer, correctAnswer) {
    if (!userAnswer || !correctAnswer) return false;

    const userInput = userAnswer.trim().toLowerCase();
    const correctText = correctAnswer.toLowerCase();

    // 1. 直接匹配
    if (userInput === correctText) return true;

    // 2. 处理特殊格式
    return this.checkSpecialFormats(userInput, correctText);
  },

  // 检查特殊格式
  checkSpecialFormats: function(userInput, correctText) {
    // 移除星号
    const cleanCorrect = correctText.replace(/\*/g, '');

    // 1. 处理逗号分隔格式 "am, is, are"
    if (cleanCorrect.includes(',')) {
      const options = cleanCorrect.split(',').map(s => s.trim());
      // 用户输入任何一个都算对
      if (options.includes(userInput)) return true;
      // 用户输入多个也算对
      const userOptions = userInput.split(/[,，]/).map(s => s.trim());
      if (userOptions.every(opt => options.includes(opt)) && userOptions.length > 0) return true;
    }

    // 2. 处理斜杠或等号分隔格式 "actor/actress", "kilo=kilogram"
    if (cleanCorrect.includes('/') || cleanCorrect.includes('=')) {
      const separators = /[\/=]/;
      const options = cleanCorrect.split(separators).map(s => s.trim());
      if (options.includes(userInput)) return true;
      // 用户输入多个也算对
      const userOptions = userInput.split(/[\/=,，]/).map(s => s.trim());
      if (userOptions.every(opt => options.includes(opt)) && userOptions.length > 0) return true;
    }

    // 3. 处理括号格式，忽略括号内容
    const withoutBrackets = cleanCorrect.replace(/\([^)]*\)/g, '').trim();
    if (userInput === withoutBrackets) return true;

    // 4. 处理带括号的可选内容 "according (to)", "(real) estate agent"
    const optionalPattern = /\(([^)]+)\)/g;
    let matches = [...cleanCorrect.matchAll(optionalPattern)];
    if (matches.length > 0) {
      // 生成所有可能的组合
      let variations = [cleanCorrect];
      matches.forEach(match => {
        const newVariations = [];
        variations.forEach(variation => {
          // 包含括号内容的版本
          newVariations.push(variation.replace(match[0], match[1]));
          // 不包含括号内容的版本
          newVariations.push(variation.replace(match[0], ''));
          // 保留括号的版本
          newVariations.push(variation);
        });
        variations = newVariations;
      });

      // 清理空格并检查匹配
      for (let variation of variations) {
        const cleaned = variation.replace(/\s+/g, ' ').trim().toLowerCase();
        if (userInput === cleaned) return true;
      }
    }

    // 5. 处理中英文标注格式 "centre（英）center（美）", "programme(美program)"
    const langPattern = /[（(][^)）]*[）)]/g;
    const withoutLangMarks = cleanCorrect.replace(langPattern, '').trim();
    if (userInput === withoutLangMarks) return true;

    // 提取所有可能的单词
    const allWords = cleanCorrect.match(/[a-zA-Z]+/g) || [];
    if (allWords.includes(userInput)) return true;

    // 6. 处理复杂等号格式 "drier"＝"dryer"
    if (cleanCorrect.includes('＝') || cleanCorrect.includes('=')) {
      const parts = cleanCorrect.split(/[＝=]/).map(s => s.replace(/["""]/g, '').trim());
      if (parts.includes(userInput)) return true;
    }

    return false;
  },

  // 提交填空题答案
  onSubmitAnswer: function() {
    console.log('提交答案被调用，当前输入:', this.data.userInput);
    console.log('是否已答题:', this.data.isAnswered);
    if (this.data.isAnswered || !this.data.userInput.trim()) {
      console.log('提交被阻止，原因:', this.data.isAnswered ? '已答题' : '输入为空');
      return;
    }

    // 停止每题计时器
    if (this.data.questionTimer) {
      clearInterval(this.data.questionTimer);
    }

    const userAnswer = this.data.userInput.trim();
    const correctAnswer = this.data.currentWord.words || this.data.currentWord.word;
    const isCorrect = this.validateAnswer(userAnswer, correctAnswer);

    // 记录错题
    if (!isCorrect) {
      const mistakeData = {
        word: this.data.currentWord,
        selectedAnswer: this.data.userInput.trim(),
        correctAnswer: this.data.currentWord.words || this.data.currentWord.word,
        timestamp: Date.now()
      };

      this.data.mistakes.push(mistakeData);

      // 在测试模式下，自动添加错题到错题本（包括分享测试）
      if (this.data.isTestMode) {
        console.log('开始收集填空错词到错题本:', mistakeData);
        this.addToMistakeBook(mistakeData);
      }
    }

    this.setData({
      isAnswered: true,
      showAnswer: true,
      isCorrect: isCorrect,
      correctCount: this.data.correctCount + (isCorrect ? 1 : 0),
      wrongCount: this.data.wrongCount + (isCorrect ? 0 : 1),
      score: this.data.score + (isCorrect ? 10 : 0)
    });

    // 自动进入下一题
    setTimeout(() => {
      this.setData({
        currentIndex: this.data.currentIndex + 1,
        userInput: '',
        showAnswer: false,
        isAnswered: false,
        isCorrect: false,
        inputFocused: false
      });
      this.nextQuestion();
    }, 1500);
  },

  // 结束测试
  endTest: function() {
    // 清理所有计时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    if (this.data.questionTimer) {
      clearInterval(this.data.questionTimer);
    }

    // 如果是测试模式，先解除考试锁定
    if (this.data.isTestMode && this.data.examMode) {
      this.disableExamMode();
    }

    // 计算准确率
    const accuracyRate = (this.data.correctCount / this.data.totalQuestions * 100).toFixed(1);

    // 检查是否有下一组和是否达到通过要求
    const hasNextGroup = this.data.isGrouped && this.data.currentGroup < this.data.totalGroups;
    const passedCurrentGroup = parseFloat(accuracyRate) >= 80; // 80%通过率要求
    const canProceedToNext = hasNextGroup && passedCurrentGroup;

    console.log('🔧 测试完成，检查下一组:', {
      isGrouped: this.data.isGrouped,
      currentGroup: this.data.currentGroup,
      totalGroups: this.data.totalGroups,
      hasNextGroup: hasNextGroup,
      accuracyRate: accuracyRate,
      passedCurrentGroup: passedCurrentGroup,
      canProceedToNext: canProceedToNext,
      wordsPerGroup: this.data.wordsPerGroup
    });

    // 生成详细结果数据
    const detailedResults = this.generateDetailedResults();
    const formattedTestTime = this.formatTestTime(Date.now() - this.data.startTime);

    this.setData({
      testCompleted: true,
      endTime: Date.now(),
      showResult: true,
      accuracyRate: accuracyRate,
      hasNextGroup: hasNextGroup,
      passedCurrentGroup: passedCurrentGroup,
      canProceedToNext: canProceedToNext,
      detailedResults: detailedResults,
      formattedTestTime: formattedTestTime,
      selectedWrongWords: new Array(this.data.wrongCount).fill(false)
    });

    // 保存测试结果
    this.saveTestResult();
  },

  // 生成详细结果数据
  generateDetailedResults: function() {
    const results = [];
    let wrongIndex = 0;

    // 遍历所有题目，生成详细结果
    for (let i = 0; i < this.data.words.length; i++) {
      const word = this.data.words[i];
      const userAnswers = this.data.userAnswers || {};
      const mistakes = this.data.mistakes || [];

      // 查找该题的用户答案
      let userAnswer = '';
      let isCorrect = true;
      let correctAnswer = '';

      // 从错题记录中查找
      const mistake = mistakes.find(m =>
        (m.word.words || m.word.word) === (word.words || word.word)
      );

      if (mistake) {
        userAnswer = mistake.selectedAnswer;
        correctAnswer = mistake.correctAnswer;
        isCorrect = false;
      } else {
        // 如果不在错题中，说明答对了
        if (this.data.testMode === 'en_to_cn') {
          correctAnswer = word.meaning;
        } else if (this.data.testMode === 'cn_to_en') {
          correctAnswer = word.words || word.word;
        }
        userAnswer = correctAnswer; // 答对的情况下，用户答案就是正确答案
        isCorrect = true;
      }

      // 🔧 判断是否为短语（长度超过10个字符或包含空格）
      const wordText = word.words || word.word || '';
      const isPhrase = wordText.length > 10 || wordText.includes(' ') || wordText.includes('(') || wordText.includes('.');

      const result = {
        word: word,
        userAnswer: userAnswer,
        correctAnswer: correctAnswer,
        isCorrect: isCorrect,
        isPhrase: isPhrase, // 添加短语标识
        questionIndex: i
      };

      // 为错误答案添加错误索引
      if (!isCorrect) {
        result.wrongIndex = wrongIndex++;
      }

      results.push(result);
    }

    console.log('生成详细结果:', {
      totalResults: results.length,
      correctCount: results.filter(r => r.isCorrect).length,
      wrongCount: results.filter(r => !r.isCorrect).length
    });

    return results;
  },

  // 格式化测试时间
  formatTestTime: function(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}分${remainingSeconds}秒`;
    } else {
      return `${remainingSeconds}秒`;
    }
  },

  // 保存测试结果
  saveTestResult: function() {
    const {
      testMode,
      correctCount,
      wrongCount,
      totalQuestions,
      score,
      mistakes,
      startTime,
      endTime,
      libraryId,
      libraryName,
      shareMode,
      shareId,
      isGrouped,
      currentGroup,
      totalGroups,
      wordsPerGroup
    } = this.data;

    const testResult = {
      testMode,
      correctCount,
      wrongCount,
      totalQuestions,
      score,
      accuracy: (correctCount / totalQuestions * 100).toFixed(1),
      duration: endTime - startTime,
      mistakes,
      libraryId,
      libraryName,
      shareMode,
      shareId,
      timestamp: Date.now()
    };

    // 如果是分享测试，保存到本地和分享数据
    if (shareMode === 'share' && shareId) {
      this.saveSharedTestResult(testResult);
    } else if (shareMode === 'competition' && this.data.competitionId) {
      // 竞赛模式，提交竞赛结果
      this.submitCompetitionResult(testResult);
    } else {
      // 自己的测试，保存到云端
      wx.cloud.callFunction({
        name: 'saveTestResult',
        data: testResult
      }).then(result => {
        console.log('测试结果保存成功:', result);
      }).catch(error => {
        console.error('测试结果保存失败:', error);
        // 云端保存失败时，也保存到本地作为备份
        this.saveToLocalTestResults(testResult);
      });

      // 更新学习进度（仅自己的测试）
      this.updateLearningProgress();
    }
  },

  // 更新学习进度
  updateLearningProgress: function() {
    const {
      libraryId,
      libraryName,
      testMode,
      isGrouped,
      currentGroup,
      totalGroups,
      wordsPerGroup,
      correctCount,
      totalQuestions,
      score
    } = this.data;

    if (!libraryId || !testMode) {
      return;
    }

    try {
      if (isGrouped && currentGroup && totalGroups && wordsPerGroup) {
        // 分组学习进度更新
        const progressKey = `progress_${libraryId}_${testMode}`;
        let progressData = wx.getStorageSync(progressKey) || {};

        // 初始化或更新分组进度数据
        if (!progressData.isGrouped) {
          progressData = {
            libraryId: libraryId,
            libraryName: libraryName,
            mode: testMode,
            isGrouped: true,
            groupInfo: {
              currentGroup: 1,
              totalGroups: totalGroups,
              wordsPerGroup: wordsPerGroup,
              completedGroups: []
            }
          };
        }

        // 检查当前组是否通过（可以设置通过标准，比如80%正确率）
        const accuracy = (correctCount / totalQuestions) * 100;
        const isPassed = accuracy >= 60; // 60%通过率

        if (isPassed && !progressData.groupInfo.completedGroups.includes(currentGroup)) {
          // 标记当前组为已完成
          progressData.groupInfo.completedGroups.push(currentGroup);
          progressData.groupInfo.completedGroups.sort((a, b) => a - b);
        }

        // 更新当前组和进度信息
        progressData.groupInfo.currentGroup = Math.max(currentGroup, progressData.groupInfo.currentGroup);
        progressData.lastStudyTime = Date.now();
        progressData.percentage = Math.round((progressData.groupInfo.completedGroups.length / totalGroups) * 100);
        progressData.currentIndex = progressData.groupInfo.completedGroups.length * wordsPerGroup;
        progressData.totalCount = totalGroups * wordsPerGroup;
        progressData.progressText = `第${currentGroup}/${totalGroups}组`;
        progressData.detailText = `已完成${progressData.groupInfo.completedGroups.length}组，共${totalGroups}组`;

        // 保存进度
        wx.setStorageSync(progressKey, progressData);
        console.log('分组学习进度已更新:', progressData);

      } else {
        // 普通学习进度更新（非分组）
        const progressKey = `progress_${libraryId}_${testMode}`;
        let progressData = wx.getStorageSync(progressKey) || {};

        // 更新基本进度信息
        progressData = {
          ...progressData,
          libraryId: libraryId,
          libraryName: libraryName,
          mode: testMode,
          lastStudyTime: Date.now(),
          isGrouped: false
        };

        wx.setStorageSync(progressKey, progressData);
        console.log('学习进度已更新:', progressData);
      }

    } catch (error) {
      console.error('更新学习进度失败:', error);
    }
  },

  // 保存分享测试结果
  // 保存分享测试结果
  async saveSharedTestResult(testResult) {
    try {
      wx.showLoading({
        title: '提交结果...',
        mask: true
      });

      const currentUser = wx.getStorageSync('userInfo') || {};
      const { shareId, currentLevelId = 1 } = this.data;

      // 获取用户昵称，尝试多种可能的字段
      let nickName = '匿名用户';
      let avatarUrl = '';

      if (currentUser.nickName) {
        nickName = currentUser.nickName;
      } else if (currentUser.wechatInfo?.nickName) {
        nickName = currentUser.wechatInfo.nickName;
      } else if (currentUser.username) {
        nickName = currentUser.username;
      } else if (currentUser.phone) {
        nickName = `用户${currentUser.phone.slice(-4)}`;
      }

      if (currentUser.avatarUrl) {
        avatarUrl = currentUser.avatarUrl;
      } else if (currentUser.wechatInfo?.avatarUrl) {
        avatarUrl = currentUser.wechatInfo.avatarUrl;
      }

      console.log('用户信息调试:', {
        currentUser: currentUser,
        extractedNickName: nickName,
        extractedAvatarUrl: avatarUrl
      });

      // 构建测试结果数据
      const submitData = {
        shareId: shareId,
        levelId: currentLevelId,
        testResult: {
          score: testResult.score,
          accuracy: testResult.accuracy,
          correctCount: testResult.correctCount,
          wrongCount: testResult.wrongCount,
          mistakes: testResult.mistakes || [],
          timeSpent: testResult.duration || 0,
          totalQuestions: testResult.totalQuestions
        },
        userInfo: {
          nickName: nickName,
          avatarUrl: avatarUrl
        }
      };
      
      console.log('提交分享测试结果:', submitData);
      
      // 优先提交到云端
      let cloudSubmitted = false;
      let levelProgress = null;
      
      try {
        const result = await wx.cloud.callFunction({
          name: 'submitShareTestResult',
          data: submitData
        });
        
        if (result.result.success) {
          cloudSubmitted = true;
          levelProgress = result.result.data.levelProgress;
          
          console.log('云端提交成功:', result.result.data);
          
          // 更新本地数据
          this.setData({
            levelProgress: levelProgress
          });
          
          // 如果是多关卡任务，检查是否解锁了新关卡
          if (this.data.isMultiLevel && levelProgress) {
            const isNewLevelUnlocked = result.result.data.levelProgress?.isNewLevelUnlocked;
            // 不再显示弹窗，而是在结果页面显示相应的按钮
            console.log('多关卡进度更新:', {
              isNewLevelUnlocked,
              currentLevel: levelProgress.currentLevel,
              totalLevels: this.data.totalLevels
            });
          }
        }
      } catch (cloudError) {
        console.log('云端提交失败，使用本地存储:', cloudError);
      }
      
      // 如果云端提交失败，使用本地存储作为备份
      if (!cloudSubmitted) {
        this.saveToLocalShareTest(testResult);
      }
      
      // 保存到本地测试结果记录
      const result = {
        ...testResult,
        shareId: shareId,
        levelId: currentLevelId,
        participantInfo: currentUser,
        participantOpenid: currentUser.openid,
        submitTime: Date.now(),
        cloudSubmitted: cloudSubmitted
      };
      
      this.saveToLocalTestResults(result);
      
      wx.hideLoading();
      
      // 显示完成提示
      wx.showToast({
        title: cloudSubmitted ? '测试结果已提交' : '测试结果已保存',
        icon: 'success',
        duration: 2000
      });
      
      console.log('分享测试结果处理完成');
      
    } catch (error) {
      wx.hideLoading();
      console.error('保存分享测试结果失败:', error);
      wx.showToast({
        title: '结果保存失败',
        icon: 'error'
      });
    }
  },

  // 保存到本地分享测试（备份机制）
  saveToLocalShareTest: function(testResult) {
    try {
      const shareTests = wx.getStorageSync('shareTests') || {};
      const shareTestData = shareTests[testResult.shareId];
      
      if (shareTestData) {
        const currentUser = wx.getStorageSync('userInfo') || {};
        
        // 添加参与者信息
        const result = {
          ...testResult,
          levelId: this.data.currentLevelId || 1,
          participantInfo: currentUser,
          participantOpenid: currentUser.openid,
          creatorOpenid: shareTestData.creatorInfo?.openid || shareTestData.createdBy,
          submitTime: Date.now()
        };
        
        if (!shareTestData.results) {
          shareTestData.results = [];
        }
        shareTestData.results.push(result);
        
        // 更新被分享人的测试次数和成绩
        if (shareTestData.visitors && currentUser.openid) {
          const visitor = shareTestData.visitors.find(v => v.openid === currentUser.openid);
          if (visitor) {
            visitor.testCount = (visitor.testCount || 0) + 1;
            visitor.lastTestTime = Date.now();
            visitor.bestScore = Math.max(visitor.bestScore || 0, testResult.score);
            visitor.totalTests = (visitor.totalTests || 0) + 1;
            visitor.totalScore = (visitor.totalScore || 0) + testResult.score;
            visitor.averageScore = Math.round(visitor.totalScore / visitor.totalTests);
            visitor.latestAccuracy = testResult.accuracy;
            visitor.latestMistakeCount = testResult.wrongCount;
          }
        }
        
        shareTests[testResult.shareId] = shareTestData;
        wx.setStorageSync('shareTests', shareTests);
        
        console.log('分享测试结果已保存到本地:', result);
      }
    } catch (error) {
      console.error('保存分享测试结果到本地失败:', error);
    }
  },

  // 显示关卡解锁对话框
  showLevelUnlockedDialog: function(levelProgress) {
    const { totalLevels } = this.data;
    const nextLevel = levelProgress.currentLevel;
    
    if (nextLevel <= totalLevels) {
      wx.showModal({
        title: '🎉 恭喜通过！',
        content: `您已成功通过第${nextLevel - 1}关！\n第${nextLevel}关已解锁，是否继续挑战？`,
        confirmText: '继续挑战',
        cancelText: '查看结果',
        success: (res) => {
          if (res.confirm) {
            this.goToNextLevel();
          }
        }
      });
    } else {
      wx.showModal({
        title: '🏆 全部通关！',
        content: `恭喜您完成了所有${totalLevels}关的挑战！\n您已经是真正的词汇大师了！`,
        showCancel: false,
        confirmText: '查看结果'
      });
    }
  },

  // 前往下一关
  goToNextLevel: function() {
    const { shareId, levelProgress, shareTestData } = this.data;
    const nextLevel = levelProgress.currentLevel;
    
    if (nextLevel <= shareTestData.totalLevels) {
      // 重定向到下一关
      wx.redirectTo({
        url: `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&levelId=${nextLevel}&testMode=${this.data.testMode}`
      });
    }
  },

  // 提交竞赛结果
  async submitCompetitionResult(testResult) {
    try {
      wx.showLoading({
        title: '提交成绩...',
        mask: true
      });

      // 获取用户信息
      const app = getApp();
      const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo') || {};
      const competitionMode = this.data.testMode === 'en_to_cn' ? 'en2zh' : 'zh2en';
      
      console.log('提交竞赛结果 - 用户信息:', {
        userInfo: userInfo,
        nickName: userInfo.wechatInfo?.nickName,
        username: userInfo.username,
        finalUserName: userInfo.wechatInfo?.nickName || userInfo.username || '匿名用户'
      });
      
      // 调用云函数提交竞赛结果
      const result = await wx.cloud.callFunction({
        name: 'submitCompetitionResult',
        data: {
          competitionId: this.data.competitionId,
          mode: competitionMode,
          score: testResult.score,
          duration: Math.round(testResult.duration / 1000), // 转换为秒
          accuracy: parseFloat(testResult.accuracy),
          correctCount: testResult.correctCount,
          totalCount: testResult.totalQuestions,
          userName: userInfo.wechatInfo?.nickName || userInfo.username || '匿名用户'
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        // 显示提交成功的提示
        if (result.result.isNewRecord) {
          wx.showToast({
            title: '新纪录！成绩已更新',
            icon: 'success',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: result.result.message || '成绩已提交',
            icon: 'success',
            duration: 2000
          });
        }
        
        // 不自动跳转到排行榜，让用户在结果页面选择下一步操作
        console.log('竞赛结果提交成功，准备显示结果页面');
        
        // 设置竞赛完成状态和检查是否有下一关
        // 优先使用云函数返回的completed状态，其次使用本地计算的结果
        const isCompleted = result.result.completed !== undefined ? 
          result.result.completed : 
          (testResult.accuracy >= 80);
        
        this.setData({
          competitionCompleted: isCompleted,
          isCompetition: true
        });
        
        // 如果是多关卡竞赛且通过了当前关卡，检查是否有下一关
        if (isCompleted && this.data.masterCompetitionId) {
          this.checkNextLevel();
          // 通知关卡选择页面刷新状态（如果存在）
          this.notifyLevelSelectRefresh();
        }
      } else {
        throw new Error(result.result.message || '提交失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('提交竞赛结果失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 保存到本地测试结果
  saveToLocalTestResults: function(testResult) {
    try {
      // 获取现有的测试结果记录
      let testResults = wx.getStorageSync('testResults') || [];
      
      // 添加新的测试结果
      testResults.push(testResult);
      
      // 限制记录数量，只保留最近的500条记录
      if (testResults.length > 500) {
        testResults = testResults.slice(-500);
      }
      
      // 保存到本地存储
      wx.setStorageSync('testResults', testResults);
      
      console.log('测试结果已保存到本地存储');
    } catch (error) {
      console.error('保存测试结果到本地失败:', error);
    }
  },

  // 重新测试
  retryTest: function() {
    // 清理所有计时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    if (this.data.questionTimer) {
      clearInterval(this.data.questionTimer);
    }

    // 🔧 修复：保存分组信息，避免重新测试时丢失分组状态
    const groupInfo = {
      isGrouped: this.data.isGrouped,
      currentGroup: this.data.currentGroup,
      totalGroups: this.data.totalGroups,
      wordsPerGroup: this.data.wordsPerGroup,
      hasNextGroup: this.data.hasNextGroup
    };

    console.log('🔧 重新测试（retryTest），保持分组信息:', groupInfo);

    const { perQuestionTime } = this.data;

    this.setData({
      currentIndex: 0,
      correctCount: 0,
      wrongCount: 0,
      score: 0,
      accuracyRate: '0.0',
      progressPercent: 0,
      timeDisplay: `${perQuestionTime}秒`,
      currentQuestionTimeLeft: perQuestionTime,
      mistakes: [],
      testStarted: false,
      testCompleted: false,
      showResult: false,
      isAnswered: false,
      selectedOption: -1,
      correctOption: -1,
      showAnswer: false,
      // 🔧 修复：保持分组信息不被重置
      isGrouped: groupInfo.isGrouped,
      currentGroup: groupInfo.currentGroup,
      totalGroups: groupInfo.totalGroups,
      wordsPerGroup: groupInfo.wordsPerGroup,
      hasNextGroup: groupInfo.hasNextGroup
    });

    this.showTestPreparation();
  },

  // 进入下一组
  goToNextGroup: function() {
    const app = getApp();
    const { originalWords, wordsPerGroup, currentGroup, totalGroups, testMode, perQuestionTime } = this.data;
    
    if (!originalWords || currentGroup >= totalGroups) {
      wx.showToast({ title: '没有更多组了', icon: 'none' });
      return;
    }
    
    // 计算下一组的开始和结束索引
    const nextGroup = currentGroup + 1;
    const startIndex = (nextGroup - 1) * wordsPerGroup;
    const endIndex = Math.min(startIndex + wordsPerGroup, originalWords.length);
    const nextGroupWords = originalWords.slice(startIndex, endIndex);
    
    console.log('进入下一组:', {
      nextGroup,
      startIndex,
      endIndex,
      nextGroupWords: nextGroupWords.length
    });
    
    // 更新全局数据
    const learningData = app.globalData.learningData;
    app.globalData.learningData = {
      ...learningData,
      words: nextGroupWords,
      currentGroup: nextGroup
    };
    
    // 重置页面数据
    this.setData({
      words: nextGroupWords,
      totalQuestions: nextGroupWords.length,
      currentGroup: nextGroup,
      currentIndex: 0,
      correctCount: 0,
      wrongCount: 0,
      score: 0,
      accuracyRate: '0.0',
      progressPercent: 0,
      timeDisplay: `${perQuestionTime}秒`,
      currentQuestionTimeLeft: perQuestionTime,
      mistakes: [],
      testStarted: false,
      testCompleted: false,
      showResult: false,
      hasNextGroup: false,
      isAnswered: false,
      selectedOption: -1,
      correctOption: -1,
      showAnswer: false
    });
    
    // 显示准备界面
    this.showTestPreparation();
  },

  // 查看错题
  viewMistakes: function() {
    if (this.data.mistakes.length === 0) {
      wx.showToast({ title: '没有错题', icon: 'none' });
      return;
    }

    // 存储错题数据
    app.globalData.testMistakes = this.data.mistakes;
    
    // 跳转到错题页面
    wx.navigateTo({
      url: '/pages/mistakes/mistakes'
    });
  },

  // 分享测试结果
  shareTestResult: function() {
    const options = ['保存成绩图片到相册', '分享给微信好友', '分享到朋友圈'];

    wx.showActionSheet({
      itemList: options,
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.saveResultImageToAlbum();
            break;
          case 1:
            this.shareToWeChat();
            break;
          case 2:
            this.shareToTimeline();
            break;
        }
      }
    });
  },

  // 生成测试结果图片
  generateResultImage: function() {
    const { testMode, score, correctCount, wrongCount, totalQuestions, accuracyRate, libraryName } = this.data;
    const modeText = testMode === 'en_to_cn' ? '英译汉' : '汉译英';
    
    // 创建画布
    const ctx = wx.createCanvasContext('resultCanvas', this);
    
    // 设置背景
    const gradient = ctx.createLinearGradient(0, 0, 375, 600);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 375, 600);
    
    // 设置字体样式
    ctx.setFillStyle('#ffffff');
    ctx.setFontSize(28);
    ctx.setTextAlign('center');
    
    // 标题
    ctx.fillText('墨词自习室 - 测试成绩', 187.5, 60);
    
    // 测试信息
    ctx.setFontSize(24);
    ctx.fillText(`${modeText} - ${libraryName}`, 187.5, 100);
    
    // 成绩卡片背景
    ctx.setFillStyle('rgba(255, 255, 255, 0.95)');
    ctx.fillRect(30, 130, 315, 340);
    
    // 成绩信息
    ctx.setFillStyle('#333333');
    ctx.setFontSize(36);
    ctx.setTextAlign('center');
    ctx.fillText(`${score}分`, 187.5, 200);
    
    ctx.setFontSize(20);
    ctx.fillText(`准确率 ${accuracyRate}%`, 187.5, 230);
    
    // 详细统计
    ctx.setFontSize(18);
    ctx.setTextAlign('left');
    
    ctx.fillText(`总题数：${totalQuestions}`, 60, 280);
    ctx.fillText(`正确数：${correctCount}`, 60, 310);
    ctx.fillText(`错误数：${wrongCount}`, 60, 340);
    
    // 时间信息
    const testTime = new Date().toLocaleString('zh-CN');
    ctx.setFontSize(14);
    ctx.setTextAlign('center');
    ctx.fillText(`测试时间：${testTime}`, 187.5, 420);
    
    ctx.draw(false, () => {
      // 保存图片到相册
      wx.canvasToTempFilePath({
        canvasId: 'resultCanvas',
        success: (res) => {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.showToast({
                title: '已保存到相册',
                icon: 'success'
              });
            },
            fail: (err) => {
              console.error('保存图片到相册失败:', err);

              if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
                wx.showModal({
                  title: '保存失败',
                  content: '需要相册权限才能保存图片，请在设置中开启相册权限',
                  confirmText: '去设置',
                  cancelText: '取消',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting();
                    }
                  }
                });
              } else if (err.errMsg.includes('privacy api banned')) {
                wx.showModal({
                  title: '保存失败',
                  content: '当前小程序版本暂不支持保存到相册功能，请联系开发者或等待版本更新',
                  showCancel: false,
                  confirmText: '知道了'
                });
              } else {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                });
              }
            }
          });
        },
        fail: (error) => {
          console.error('生成图片失败:', error);
          wx.showToast({
            title: '生成图片失败',
            icon: 'none'
          });
        }
      }, this);
    });
  },

  // 分享到微信
  shareToWeChat: function() {
    const { testMode, score, correctCount, totalQuestions, accuracyRate, libraryName } = this.data;
    const modeText = testMode === 'en_to_cn' ? '英译汉' : '汉译英';
    
    // 设置分享数据
    this.setData({
      currentShareData: {
        title: `我在墨词自习室${modeText}测试中获得了${score}分！`,
        path: `/pages/wordtest/mode-select/mode-select?from=share`,
        imageUrl: '/assets/icons/logo.png'
      },
      showShareModal: true
    });
  },

  // 复制分享链接
  copyResultLink: function() {
    const { testMode, score, correctCount, totalQuestions, accuracyRate, libraryName } = this.data;
    const modeText = testMode === 'en_to_cn' ? '英译汉' : '汉译英';
    
    const shareText = `🎯 墨词自习室测试成绩分享\n\n📚 ${modeText} - ${libraryName}\n📊 得分：${score}分\n✅ 正确率：${accuracyRate}%\n📝 答题：${correctCount}/${totalQuestions}\n\n快来挑战吧！`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 关闭分享弹窗
  closeShareModal: function() {
    this.setData({
      showShareModal: false
    });
  },

  // 分享按钮点击
  onShareButtonTap: function() {
    console.log('分享按钮被点击');
    // 关闭分享弹窗
    this.setData({
      showShareModal: false
    });
  },

  // 保存成绩图片到相册（替换复制分享信息功能）
  saveResultToAlbum: function() {
    // 关闭分享弹窗
    this.setData({
      showShareModal: false
    });
    
    // 调用生成图片功能
    this.generateResultImage();
  },

  // 页面分享回调
  onShareAppMessage: function() {
    const shareData = this.data.currentShareData;
    if (shareData) {
      return shareData;
    }
    
    // 默认分享数据
    const { testMode, score, libraryName } = this.data;
    const modeText = testMode === 'en_to_cn' ? '英译汉' : '汉译英';
    
    return {
      title: `我在墨词自习室${modeText}测试中获得了${score}分！`,
      path: `/pages/wordtest/mode-select/mode-select?from=share`,
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 返回主页
  backToHome: function() {
    // 检查页面栈，如果只有一个页面或者前一个页面是主页，则使用switchTab
    const pages = getCurrentPages();
    if (pages.length <= 1) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    } else {
      const prevPage = pages[pages.length - 2];
      if (prevPage && prevPage.route === 'pages/index/index') {
        wx.switchTab({
          url: '/pages/index/index'
        });
      } else {
        wx.navigateBack();
      }
    }
  },

  // 格式化时间
  formatTime: function(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  // 练习模式 - 下一个词汇
  nextPracticeWord: function() {
    if (this.data.currentIndex >= this.data.words.length) {
      this.endPractice();
      return;
    }

    const currentWord = this.data.words[this.data.currentIndex];
    const progressPercent = ((this.data.currentIndex + 1) / this.data.totalQuestions * 100);
    

    
    this.setData({
      currentWord: currentWord,
      progressPercent: progressPercent,
      showMeaning: false, // 重置显示状态
      showExample: false
    });
  },

  // 练习模式 - 下一个词汇
  onPracticeNext: function() {
    this.setData({
      currentIndex: this.data.currentIndex + 1
    });
    this.nextPracticeWord();
  },

  // 练习模式 - 上一个词汇
  onPracticePrev: function() {
    if (this.data.currentIndex > 0) {
      this.setData({
        currentIndex: this.data.currentIndex - 1
      });
      this.nextPracticeWord();
    }
  },

  // 练习模式 - 显示/隐藏释义
  toggleMeaning: function() {
    this.setData({
      showMeaning: !this.data.showMeaning
    });
  },

  // 练习模式 - 显示/隐藏例句
  toggleExample: function() {
    this.setData({
      showExample: !this.data.showExample
    });
  },

  // 练习模式 - 已掌握，进入下一个
  onMastered: function() {
    this.setData({
      currentIndex: this.data.currentIndex + 1
    });
    this.nextPracticeWord();
  },

  // 结束练习
  endPractice: function() {
    this.setData({
      testCompleted: true,
      endTime: Date.now(),
      showResult: true
    });
  },

  // 朋友圈分享配置
  onShareTimeline() {
    // 优先使用专门为朋友圈设置的分享数据
    const timelineShareData = this.data.currentTimelineShareData;
    if (timelineShareData) {
      return timelineShareData;
    }

    // 其次使用普通分享数据
    const shareData = this.data.currentShareData;
    if (shareData) {
      return {
        title: shareData.title + ' 快来挑战吧！',
        query: 'from=timeline',
        imageUrl: shareData.imageUrl
      };
    }

    // 默认朋友圈分享数据
    const { testMode, score, libraryName } = this.data;
    const modeText = testMode === 'en_to_cn' ? '英译汉' : '汉译英';

    return {
      title: `我在墨词自习室${modeText}测试中获得了${score}分！快来挑战吧！`,
      query: 'from=timeline',
      imageUrl: '/assets/icons/logo.png'
    };
  },



  // 获取用户OpenID
  getOpenId: function() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            wx.cloud.callFunction({
              name: 'login',
              data: { code: res.code },
              success: (result) => {
                if (result.result && result.result.success) {
                  const openid = result.result.data.openid;
                  getApp().globalData.openid = openid;
                  wx.setStorageSync('openid', openid);
                  resolve(openid);
                } else {
                  reject(new Error('登录验证失败'));
                }
              },
              fail: reject
            });
          } else {
            reject(new Error('获取登录凭证失败'));
          }
        },
        fail: reject
      });
    });
  },

  // ================ 发音功能 ================
  
  // 初始化音频上下文
  initAudioContext: function() {
    try {
      // 先清理旧的音频上下文
      if (this.data.audioContext) {
        try {
          if (typeof this.data.audioContext.destroy === 'function') {
            this.data.audioContext.destroy();
          }
        } catch (error) {
          console.warn('清理旧音频上下文失败:', error);
        }
      }

      const audioContext = wx.createInnerAudioContext();

      audioContext.onPlay(() => {
        this.setData({ isPlaying: true });
      });

      audioContext.onEnded(() => {
        this.setData({ isPlaying: false });
      });

      audioContext.onError((error) => {
        console.error('音频播放错误:', error);
        this.setData({ isPlaying: false });
        wx.showToast({
          title: '播放失败',
          icon: 'none'
        });
      });

      this.setData({ audioContext });
      console.log('音频上下文初始化成功');
    } catch (error) {
      console.error('初始化音频上下文失败:', error);
      this.setData({ audioContext: null });
    }
  },

  // 播放单词发音
  onPlayWord: async function() {
    if (!this.data.currentWord) {
      wx.showToast({
        title: '当前没有单词',
        icon: 'none'
      });
      return;
    }

    if (this.data.isPlaying) {
      // 如果正在播放，停止播放
      this.data.audioContext.stop();
      this.setData({ isPlaying: false });
      return;
    }

    const word = this.data.currentWord.words || this.data.currentWord.word;
    
    // 在练习模式下，默认播放两遍，没有间隔
    if (!this.data.isTestMode) {
      await this.playWordTwice(word);
    } else {
      // 测试模式下，只播放一遍
      await this.playWordOnce(word);
    }
  },

  // 播放单词两遍（练习模式）
  playWordTwice: async function(word) {
    try {
      console.log('练习模式：开始播放单词两遍');

      // 调用TTS云函数获取播放列表
      const playList = await this.getWordPlayList(word, 'practice', 2);
      if (!playList || playList.length === 0) {
        console.error('无法获取单词播放列表');
        wx.showToast({
          title: '获取音频失败',
          icon: 'none'
        });
        return;
      }

      // 播放列表中的所有音频
      await this.playAudioSequence(playList);

      console.log('练习模式：单词播放完成');
    } catch (error) {
      console.error('播放单词两遍失败:', error);
      wx.showToast({
        title: '播放失败',
        icon: 'none'
      });
    }
  },

  // 播放单词一遍
  playWordOnce: async function(word) {
    return new Promise(async (resolve, reject) => {
      try {
        // 调用TTS云函数获取播放列表
        const playList = await this.getWordPlayList(word, 'practice', 1);
        if (!playList || playList.length === 0) {
          console.error('无法获取单词播放列表');
          wx.showToast({
            title: '获取音频失败',
            icon: 'none'
          });
          resolve();
          return;
        }

        // 播放列表中的所有音频
        await this.playAudioSequence(playList);
        resolve();

      } catch (error) {
        console.error('播放单词发音失败:', error);
        wx.showToast({
          title: '播放失败',
          icon: 'none'
        });
        resolve();
      }
    });
  },

  // 获取单词播放列表
  async getWordPlayList(word, mode = 'practice', playCount = 2) {
    try {
      console.log(`获取单词播放列表: ${word}, 模式: ${mode}, 次数: ${playCount}`);

      // 检查缓存
      const cacheKey = `wordtest_audio_${word}_${mode}_${playCount}`;
      let cachedPlayList = this.data[cacheKey];

      if (cachedPlayList) {
        console.log(`✅ 使用缓存播放列表: ${word}`);
        return cachedPlayList;
      }

      console.log(`🔄 第一次获取，调用API: ${word}`);

      // 根据当前页面的测试模式决定wordType
      const wordType = this.data.isTestMode ? 'dictation' : 'translation';

      const result = await wx.cloud.callFunction({
        name: 'ttsSpeak',
        data: {
          text: word,
          mode: mode,
          playCount: 1, // API只获取一次音频
          wordType: wordType // 测试模式用dictation，练习模式用translation
        }
      });

      console.log('TTS云函数返回结果:', result);

      if (result.result && result.result.success) {
        const { playList, source } = result.result.data;

        // 根据实际需要的播放次数调整playList
        const adjustedPlayList = playList.map(item => ({
          ...item,
          playTimes: playCount
        }));

        // 缓存播放列表
        this.setData({
          [cacheKey]: adjustedPlayList
        });

        return adjustedPlayList;
      } else {
        console.error('TTS云函数调用失败:', result.result);
        throw new Error('获取音频失败');
      }
    } catch (error) {
      console.error('调用TTS云函数失败:', error);
      throw error;
    }
  },



  // 播放音频序列
  async playAudioSequence(playList) {
    if (!playList || playList.length === 0) {
      console.error('播放列表为空');
      return;
    }

    console.log('开始播放音频序列:', playList);

    for (const playItem of playList) {
      const { word, playTimes, audioUrl } = playItem;

      if (!audioUrl) {
        console.error(`音频URL为空: ${word}`);
        continue;
      }

      for (let i = 0; i < playTimes; i++) {
        try {
          if (i === 0) {
            console.log(`🔄 第1遍播放（API音频）: ${word} - ${audioUrl}`);
          } else {
            console.log(`✅ 第${i + 1}遍播放（缓存音频）: ${word} - ${audioUrl}`);
          }
          await this.playAudioFromUrl(audioUrl);

          // 播放间隔
          if (i < playTimes - 1) {
            await this.delay(1000); // 1秒间隔
          }
        } catch (error) {
          console.error(`播放失败: ${word}`, error);
          throw error; // 不再降级，直接抛出错误
        }
      }

      // 每个单词之间的间隔
      if (playList.indexOf(playItem) < playList.length - 1) {
        await this.delay(500); // 500ms间隔
      }
    }

    console.log('音频序列播放完成');
  },



  // 添加错题到错题本
  addToMistakeBook: function(mistakeData) {
    console.log('addToMistakeBook被调用，错词数据:', mistakeData);

    const app = getApp();

    // 使用更可靠的登录状态检查
    if (!app.canCollectMistakes()) {
      console.log('用户未登录或openid不可用，跳过错题收集');
      return;
    }

    const userId = app.getUserOpenId();
    console.log('用户ID:', userId);
    
    // 构建错题数据
    const mistakeRecord = {
      word: mistakeData.word.words || mistakeData.word.word || mistakeData.word.english,
      meaning: mistakeData.word.meaning || mistakeData.word.chinese,
      phonetic: mistakeData.word.phonetic || '',
      example: mistakeData.word.example || '',
      userAnswer: mistakeData.selectedAnswer,
      correctAnswer: mistakeData.correctAnswer,
      mistakeType: this.data.isCompetition ? 'competition' : 'wordtest', // 区分竞赛和单词检测
      testMode: this.data.isPhrase ? (this.data.testMode === 'en_to_cn' ? 'phrase_en2zh' : 'phrase_zh2en') : this.data.testMode, // 英译汉、汉译英等模式
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName,
      createTime: new Date()
    };

    console.log('构建的错题记录:', mistakeRecord);

    const cloudFunctionData = {
      userId: userId, // 传递给云函数的参数名保持userId，云函数内部会映射为_openid
      wordId: mistakeRecord.word,
      type: 'word', // 统一使用word类型，通过testMode区分
      extra: mistakeRecord
    };

    console.log('准备调用addMistake云函数，参数:', cloudFunctionData);

    wx.cloud.callFunction({
      name: 'addMistake',
      data: cloudFunctionData
    }).then(result => {
      console.log('错题已自动添加到错题本，云函数返回:', result);
    }).catch(error => {
      console.error('自动添加错题失败:', error);
      // 静默失败，不影响测试流程
    });
  },



  // 延时函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // 检查是否有下一关可用
  async checkNextLevel() {
    try {
      if (!this.data.masterCompetitionId) {
        return;
      }

      // 获取分组竞赛详情，检查下一关状态
      const result = await wx.cloud.callFunction({
        name: 'getCompetitions',
        data: {
          action: 'getGroupedCompetitionDetail',
          masterCompetitionId: this.data.masterCompetitionId
        }
      });

      if (result.result.success) {
        const data = result.result.data;
        const levels = data.levels || [];
        
        // 找到当前关卡在列表中的位置
        const currentLevelIndex = levels.findIndex(level => level.id === this.data.competitionId);
        const hasNext = currentLevelIndex >= 0 && currentLevelIndex < levels.length - 1;
        
        console.log('检查下一关:', {
          currentLevelIndex,
          totalLevels: levels.length,
          hasNext,
          nextLevelId: hasNext ? levels[currentLevelIndex + 1].id : null
        });
        
        this.setData({
          hasNextLevel: hasNext,
          nextLevelId: hasNext ? levels[currentLevelIndex + 1].id : null,
          nextLevelNumber: hasNext ? levels[currentLevelIndex + 1].levelNumber : null
        });
      }
    } catch (error) {
      console.error('检查下一关失败:', error);
    }
  },

  // 进入下一关竞赛
  async goToNextCompetitionLevel() {
    if (!this.data.nextLevelId) {
      wx.showToast({
        title: '没有下一关了',
        icon: 'none'
      });
      return;
    }

    // 检查当前关卡的正确率是否达到80%
    const currentAccuracy = parseFloat(this.data.accuracyRate);
    if (currentAccuracy < 80) {
      wx.showModal({
        title: '正确率不达标',
        content: `当前正确率为${this.data.accuracyRate}%，需要达到80%以上才能进入下一关。\n\n是否重新挑战本关？`,
        confirmText: '重新挑战',
        cancelText: '返回列表',
        success: (res) => {
          if (res.confirm) {
            // 重新开始当前关卡
            this.restartTest();
          } else {
            // 返回关卡列表
            this.backToLevelSelect();
          }
        }
      });
      return;
    }

    wx.showLoading({
      title: '加载下一关...',
      mask: true
    });

    try {
      // 跳转到下一关
      wx.redirectTo({
        url: `/pages/wordtest/test/test?competitionId=${this.data.nextLevelId}&mode=${this.data.testMode === 'en_to_cn' ? 'en2zh' : 'zh2en'}&shareMode=competition&masterCompetitionId=${this.data.masterCompetitionId}`
      });
    } catch (error) {
      wx.hideLoading();
      console.error('进入下一关失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 查看竞赛排行榜
  viewCompetitionRanking() {
    const competitionMode = this.data.testMode === 'en_to_cn' ? 'en2zh' : 'zh2en';
    const { competitionId, masterCompetitionId } = this.data;

    if (!competitionId) {
      wx.showToast({
        title: '竞赛信息不完整',
        icon: 'none'
      });
      return;
    }

    // 如果是多关卡竞赛，直接查看整体排行榜（不是单个关卡的排行榜）
    if (masterCompetitionId) {
      wx.navigateTo({
        url: `/pages/competition/ranking/ranking?competitionId=${masterCompetitionId}&mode=${competitionMode}&type=master`,
        fail: (err) => {
          console.error('跳转排行榜失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 单一竞赛，直接查看排行榜
      wx.navigateTo({
        url: `/pages/competition/ranking/ranking?competitionId=${competitionId}&mode=${competitionMode}`,
        fail: (err) => {
          console.error('跳转排行榜失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 进入下一关（分享测试多关卡模式）
  goToNextShareLevel: function() {
    const { shareId, currentLevelId, totalLevels, testMode } = this.data;

    if (currentLevelId >= totalLevels) {
      wx.showToast({
        title: '已经是最后一关了',
        icon: 'none'
      });
      return;
    }

    const nextLevelId = currentLevelId + 1;

    // 跳转到下一关
    wx.redirectTo({
      url: `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${testMode}&levelId=${nextLevelId}`
    });
  },

  // 重新挑战当前关卡
  retryCurrentLevel: function() {
    const { shareId, currentLevelId, testMode } = this.data;

    // 重新开始当前关卡
    wx.redirectTo({
      url: `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${testMode}&levelId=${currentLevelId}`
    });
  },

  // 返回分享列表
  backToShareList: function() {
    wx.navigateTo({
      url: '/pages/profile/received/received'
    });
  },

  // 关闭测试结果页面
  closeTestResult: function() {
    this.setData({
      showResult: false
    });
  },

  // 全选错误单词
  selectAllWrongWords: function() {
    const wrongCount = this.data.wrongCount;
    const currentSelected = this.data.selectedWrongWords;
    const allSelected = currentSelected.every(selected => selected);

    // 如果已经全选，则取消全选；否则全选
    this.setData({
      selectedWrongWords: new Array(wrongCount).fill(!allSelected)
    });
  },

  // 切换错误单词选择状态
  toggleWrongWordSelection: function(e) {
    const index = e.currentTarget.dataset.index;
    if (index === undefined || index === null) return;

    const selectedWrongWords = [...this.data.selectedWrongWords];
    selectedWrongWords[index] = !selectedWrongWords[index];

    this.setData({
      selectedWrongWords: selectedWrongWords
    });
  },

  // 获取选中的错误单词
  getSelectedWrongWords: function() {
    const detailedResults = this.data.detailedResults || [];
    const selectedWrongWords = this.data.selectedWrongWords || [];

    const selectedWords = [];
    detailedResults.forEach(result => {
      if (!result.isCorrect && result.wrongIndex !== undefined && selectedWrongWords[result.wrongIndex]) {
        selectedWords.push(result.word);
      }
    });

    return selectedWords;
  },

  // 重新测试选中的错误单词（相同模式）
  retrySelectedWords: function() {
    const selectedWords = this.getSelectedWrongWords();

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请先选择要重新测试的单词',
        icon: 'none'
      });
      return;
    }

    this.startRetryTest(selectedWords, this.data.testMode);
  },

  // 用相反模式重新测试选中的错误单词
  retryWithOppositeMode: function() {
    const selectedWords = this.getSelectedWrongWords();

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请先选择要重新测试的单词',
        icon: 'none'
      });
      return;
    }

    const oppositeMode = this.data.testMode === 'en_to_cn' ? 'cn_to_en' : 'en_to_cn';
    this.startRetryTest(selectedWords, oppositeMode);
  },

  // 用听写模式重新测试选中的错误单词
  retryWithDictation: function() {
    const selectedWords = this.getSelectedWrongWords();

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请先选择要重新测试的单词',
        icon: 'none'
      });
      return;
    }

    this.startRetryTest(selectedWords, 'dictation');
  },

  // 用消消乐模式重新测试选中的错误单词
  retryWithGame: function() {
    const selectedWords = this.getSelectedWrongWords();

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请先选择要重新测试的单词',
        icon: 'none'
      });
      return;
    }

    this.startRetryTest(selectedWords, 'elimination');
  },

  // 分享选中的错误单词给他人测试
  shareSelectedWords: function() {
    const selectedWords = this.getSelectedWrongWords();

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请先选择要分享的单词',
        icon: 'none'
      });
      return;
    }

    // 存储选中的单词到全局数据
    const app = getApp();
    app.globalData.learningData = {
      words: selectedWords,
      mode: this.data.testMode,
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName + '(错词重测)',
      isFromMistakes: true
    };

    // 跳转到模式选择页面，设置为分享模式
    wx.navigateTo({
      url: `/pages/wordtest/mode-select/mode-select?testMode=${this.data.testMode}&total=${selectedWords.length}&shareMode=share&libraryId=${this.data.libraryId}`
    });
  },

  // 开始重新测试
  startRetryTest: function(words, testMode) {
    // 存储选中的单词到全局数据
    const app = getApp();
    app.globalData.learningData = {
      words: words,
      mode: testMode,
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName + '(错词重测)',
      isFromMistakes: true
    };

    console.log('开始重新测试:', {
      wordsCount: words.length,
      testMode: testMode,
      firstWord: words[0]?.words || words[0]?.word
    });

    // 根据测试模式跳转到对应页面
    if (testMode === 'dictation') {
      wx.navigateTo({
        url: `/pages/spelling/mode-select/mode-select?testMode=dictation&total=${words.length}&libraryId=${this.data.libraryId}`
      });
    } else if (testMode === 'elimination') {
      wx.navigateTo({
        url: `/pages/wordtest/mode-select/mode-select?testMode=elimination&total=${words.length}&libraryId=${this.data.libraryId}`
      });
    } else {
      wx.navigateTo({
        url: `/pages/wordtest/mode-select/mode-select?testMode=${testMode}&total=${words.length}&libraryId=${this.data.libraryId}`
      });
    }
  },

  // 重新开始测试
  restartTest() {
    // 🔧 修复：保存分组信息，避免重新测试时丢失分组状态
    const groupInfo = {
      isGrouped: this.data.isGrouped,
      currentGroup: this.data.currentGroup,
      totalGroups: this.data.totalGroups,
      hasNextGroup: this.data.hasNextGroup
    };

    console.log('🔧 重新测试，保持分组信息:', groupInfo);

    // 重置测试状态
    this.setData({
      currentQuestionIndex: 0,
      correctCount: 0,
      wrongCount: 0,
      score: 0,
      accuracyRate: '0.0',
      progressPercent: 0,
      testCompleted: false,
      showResult: false,
      startTime: Date.now(),
      endTime: null,
      userAnswers: {},
      mistakes: [],
      // 🔧 修复：保持分组信息不被重置
      isGrouped: groupInfo.isGrouped,
      currentGroup: groupInfo.currentGroup,
      totalGroups: groupInfo.totalGroups,
      hasNextGroup: groupInfo.hasNextGroup
    });

    // 重新开始测试
    this.startTest();
  },

  // 返回关卡选择页面
  backToLevelSelect() {
    if (this.data.masterCompetitionId) {
      wx.navigateTo({
        url: `/pages/competition/level-select/level-select?masterCompetitionId=${this.data.masterCompetitionId}&mode=${this.data.testMode === 'en_to_cn' ? 'en2zh' : 'zh2en'}`
      });
    } else {
      wx.navigateBack();
    }
  },

  // 通知关卡选择页面刷新状态
  notifyLevelSelectRefresh() {
    // 通过全局事件总线通知关卡选择页面刷新
    const app = getApp();
    if (app.globalData) {
      app.globalData.needRefreshLevelSelect = true;
      app.globalData.refreshTimestamp = Date.now();
    }
    
    // 同时通过页面栈查找关卡选择页面并触发刷新
    const pages = getCurrentPages();
    for (let i = pages.length - 1; i >= 0; i--) {
      const page = pages[i];
      if (page.route.includes('level-select') && typeof page.refreshLevelStatus === 'function') {
        console.log('找到关卡选择页面，触发刷新');
        page.refreshLevelStatus();
        break;
      }
    }
  },

  // 从URL播放音频
  async playAudioFromUrl(audioUrl) {
    return new Promise((resolve, reject) => {
      if (!this.data.audioContext) {
        this.initAudioContext();
      }

      const audioContext = this.data.audioContext;

      // 清除之前的事件监听器
      audioContext.offEnded();
      audioContext.offError();

      // 设置事件监听器
      audioContext.onEnded(() => {
        console.log('音频播放完成');
        this.setData({ isPlaying: false });
        resolve();
      });

      audioContext.onError((error) => {
        console.error('音频播放错误:', error);
        this.setData({ isPlaying: false });
        reject(error);
      });

      // 播放音频
      this.setData({ isPlaying: true });
      audioContext.src = audioUrl;
      audioContext.play();

      // 设置超时
      setTimeout(() => {
        if (this.data.isPlaying) {
          audioContext.stop();
          this.setData({ isPlaying: false });
          reject(new Error('音频播放超时'));
        }
      }, 10000); // 10秒超时
    });
  },

  // 测试特殊格式发音
  async testSpecialFormatAudio() {
    const testWords = [
      'airplane（美）aeroplane（英）',
      'afterward（美）afterwards（英）',
      'pay(paid,paid)',
      'am(was,been)',
      'go(went,gone)',
      'a.m./A.M.',
      'p.m./P.M.',
      'etc./ETC.'
    ];
    
    wx.showModal({
      title: '测试特殊格式发音',
      content: '选择要测试的单词格式',
      showCancel: false,
      success: () => {
        wx.showActionSheet({
          itemList: testWords,
          success: async (res) => {
            const selectedWord = testWords[res.tapIndex];
            console.log('测试单词:', selectedWord);
            
            wx.showLoading({ title: '获取播放列表...' });
            
            try {
              // 测试练习模式
              console.log('=== 测试练习模式 ===');
              const practiceList = await this.getWordPlayList(selectedWord, 'practice', 2);
              console.log('练习模式播放列表:', practiceList);
              
              wx.hideLoading();
              
              if (practiceList && practiceList.length > 0) {
                wx.showModal({
                  title: '播放模式',
                  content: `练习模式播放列表已获取（${practiceList.length}项），是否播放？`,
                  confirmText: '播放',
                  cancelText: '取消',
                  success: async (modalRes) => {
                    if (modalRes.confirm) {
                      wx.showLoading({ title: '播放中...' });
                      try {
                        await this.playAudioSequence(practiceList);
                        wx.hideLoading();
                        wx.showToast({ title: '播放完成', icon: 'success' });
                      } catch (error) {
                        wx.hideLoading();
                        wx.showToast({ title: '播放失败', icon: 'none' });
                        console.error('播放失败:', error);
                      }
                    }
                  }
                });
              } else {
                wx.showToast({ title: '未获取到播放列表', icon: 'none' });
              }
              
            } catch (error) {
              wx.hideLoading();
              console.error('测试失败:', error);
              wx.showToast({ 
                title: '测试失败: ' + error.message, 
                icon: 'none',
                duration: 3000
              });
            }
          },
          fail: () => {
            console.log('用户取消选择');
          }
        });
      }
    });
  },

  // 保存成绩图片到相册
  saveResultImageToAlbum() {
    // 检查微信版本和环境
    const systemInfo = wx.getDeviceInfo();
    console.log('保存图片 - 设备信息:', {
      platform: systemInfo.platform
    });

    // 先尝试直接保存，如果失败再处理权限
    this.attemptDirectSave();
  },

  // 尝试直接保存（绕过权限检查）
  attemptDirectSave() {
    wx.showLoading({
      title: '生成图片中...',
      mask: true
    });

    // 生成成绩图片并保存到相册
    this.generateResultImageForSave().then(() => {
      wx.canvasToTempFilePath({
        canvasId: 'testResultCanvas',
        success: (res) => {
          // 直接尝试保存，不预先检查权限
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading();
              wx.showToast({
                title: '已保存到相册',
                icon: 'success'
              });
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('直接保存失败:', err);
              // 如果直接保存失败，尝试权限处理流程
              this.handleSaveFailure(err, res.tempFilePath);
            }
          });
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('生成图片失败:', err);
          this.showFallbackOptions();
        }
      }, this);
    }).catch((err) => {
      wx.hideLoading();
      console.error('生成图片失败:', err);
      this.showFallbackOptions();
    });
  },

  // 处理保存失败的情况
  handleSaveFailure(err, tempFilePath) {
    console.error('保存图片失败详情:', err);

    if (err.errMsg.includes('privacy api banned') || err.errMsg.includes('banned')) {
      // API被禁用，提供替代方案
      this.showPrivacyApiBannedDialog();
    } else if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
      // 权限问题，尝试权限流程
      this.handlePermissionFlow(tempFilePath);
    } else {
      // 其他错误，显示通用错误处理
      this.showFallbackOptions();
    }
  },

  // 显示隐私API被禁用的对话框
  showPrivacyApiBannedDialog() {
    wx.showModal({
      title: '保存功能暂不可用',
      content: '由于微信政策限制，当前版本暂时无法直接保存图片到相册。\n\n建议您：\n1. 截屏保存当前页面\n2. 等待后续版本更新\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 提示用户截屏
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },

  // 处理权限流程
  handlePermissionFlow(tempFilePath) {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启相册权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      // 用户开启了权限，重新尝试保存
                      this.retrySaveImage(tempFilePath);
                    }
                  }
                });
              }
            }
          });
        } else {
          // 尝试请求权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.retrySaveImage(tempFilePath);
            },
            fail: () => {
              this.showFallbackOptions();
            }
          });
        }
      },
      fail: () => {
        this.showFallbackOptions();
      }
    });
  },

  // 重新尝试保存图片
  retrySaveImage(tempFilePath) {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: () => {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('重试保存失败:', err);
        this.showFallbackOptions();
      }
    });
  },

  // 显示备选方案
  showFallbackOptions() {
    wx.showModal({
      title: '保存失败',
      content: '图片保存失败，您可以：\n\n1. 截屏保存当前页面\n2. 稍后重试\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '稍后重试',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },



  // 生成测试结果图片用于保存
  generateResultImageForSave() {
    return new Promise((resolve, reject) => {
      const { testMode, score, correctCount, wrongCount, totalQuestions, accuracyRate, libraryName } = this.data;
      const modeText = testMode === 'en_to_cn' ? '英译汉' : '汉译英';

      // 创建画布
      const ctx = wx.createCanvasContext('testResultCanvas', this);

      // 设置背景
      const gradient = ctx.createLinearGradient(0, 0, 375, 600);
      gradient.addColorStop(0, '#667eea');
      gradient.addColorStop(1, '#764ba2');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, 375, 600);

      // 设置字体样式
      ctx.setFillStyle('#ffffff');
      ctx.setFontSize(28);
      ctx.setTextAlign('center');

      // 标题
      ctx.fillText('墨词自习室 - 测试成绩', 187.5, 60);

      // 测试信息
      ctx.setFontSize(24);
      ctx.fillText(`${modeText}测试`, 187.5, 100);
      ctx.setFontSize(18);
      ctx.fillText(`词库：${libraryName}`, 187.5, 130);

      // 成绩卡片背景
      ctx.setFillStyle('rgba(255, 255, 255, 0.95)');
      ctx.fillRect(30, 150, 315, 300);

      // 成绩信息
      ctx.setFillStyle('#333333');
      ctx.setFontSize(36);
      ctx.setTextAlign('center');
      ctx.fillText(`${score}分`, 187.5, 220);

      ctx.setFontSize(20);
      ctx.fillText(`最终得分`, 187.5, 250);

      // 详细统计
      ctx.setFontSize(18);
      ctx.setTextAlign('left');

      ctx.fillText(`正确：${correctCount}题`, 60, 300);
      ctx.fillText(`错误：${wrongCount}题`, 200, 300);
      ctx.fillText(`总题数：${totalQuestions}题`, 60, 330);
      ctx.fillText(`正确率：${accuracyRate}%`, 200, 330);

      // 时间信息
      const testTime = new Date().toLocaleString('zh-CN');
      ctx.setFontSize(14);
      ctx.setTextAlign('center');
      ctx.fillText(`测试时间：${testTime}`, 187.5, 400);

      ctx.draw(false, () => {
        resolve();
      });
    });
  },

  // 分享到朋友圈
  shareToTimeline() {
    const { testMode, score, correctCount, totalQuestions, accuracyRate, libraryName } = this.data;
    const modeText = testMode === 'en_to_cn' ? '英译汉' : '汉译英';

    // 设置朋友圈分享数据
    this.setData({
      currentTimelineShareData: {
        title: `我在墨词自习室${modeText}测试中得了${score}分！正确率${accuracyRate}%，快来挑战吧！`,
        query: 'from=timeline',
        imageUrl: '/assets/icons/logo.png'
      }
    });

    // 更新分享菜单，启用朋友圈分享
    wx.updateShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        // 直接提示用户朋友圈分享已准备好
        wx.showToast({
          title: '朋友圈分享已准备好',
          icon: 'success',
          duration: 1500
        });

        // 延迟提示用户使用右上角分享
        setTimeout(() => {
          wx.showModal({
            title: '分享到朋友圈',
            content: '请点击右上角"..."按钮，选择"分享到朋友圈"来分享您的测试成绩！',
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1600);
      },
      fail: () => {
        wx.showToast({
          title: '分享功能暂不可用',
          icon: 'none'
        });
      }
    });
  },

  // ================ 音效播放功能 ================

  // 播放正确答案音效
  playCorrectSound: function() {
    try {
      // 使用微信小程序的音频上下文播放音效
      const audioContext = wx.createInnerAudioContext();

      // 生成正确答案的音效数据（高音调的提示音）
      const correctSoundData = this.generateCorrectSoundData();

      if (correctSoundData) {
        audioContext.src = correctSoundData;
        audioContext.volume = 0.5;
        audioContext.play();

        audioContext.onEnded(() => {
          try {
            if (audioContext && typeof audioContext.destroy === 'function') {
              audioContext.destroy();
            }
          } catch (error) {
            console.error('销毁正确音效上下文失败:', error);
          }
        });

        audioContext.onError(() => {
          try {
            if (audioContext && typeof audioContext.destroy === 'function') {
              audioContext.destroy();
            }
          } catch (error) {
            console.error('销毁正确音效上下文失败:', error);
          }
        });
      }
    } catch (error) {
      console.log('播放正确音效失败:', error);
    }
  },

  // 播放错误答案音效
  playWrongSound: function() {
    try {
      // 使用微信小程序的音频上下文播放音效
      const audioContext = wx.createInnerAudioContext();

      // 生成错误答案的音效数据（低音调的提示音）
      const wrongSoundData = this.generateWrongSoundData();

      if (wrongSoundData) {
        audioContext.src = wrongSoundData;
        audioContext.volume = 0.5;
        audioContext.play();

        audioContext.onEnded(() => {
          try {
            if (audioContext && typeof audioContext.destroy === 'function') {
              audioContext.destroy();
            }
          } catch (error) {
            console.error('销毁错误音效上下文失败:', error);
          }
        });

        audioContext.onError(() => {
          try {
            if (audioContext && typeof audioContext.destroy === 'function') {
              audioContext.destroy();
            }
          } catch (error) {
            console.error('销毁错误音效上下文失败:', error);
          }
        });
      }
    } catch (error) {
      console.log('播放错误音效失败:', error);
    }
  },

  // 生成正确答案音效数据
  generateCorrectSoundData: function() {
    try {
      // 创建一个简单的正确提示音（高音调）
      // 由于微信小程序限制，我们使用预设的音频URL或者系统音效

      // 方案1：使用系统提示音（如果可用）
      if (wx.playBackgroundAudio) {
        // 播放系统成功音效
        return null; // 使用其他方式
      }

      // 方案2：使用在线音效资源（需要配置合法域名）
      // return 'https://your-domain.com/sounds/correct.mp3';

      // 方案3：使用本地音效文件
      return '/assets/sounds/correct.wav';

    } catch (error) {
      console.log('生成正确音效数据失败:', error);
      return null;
    }
  },

  // 生成错误答案音效数据
  generateWrongSoundData: function() {
    try {
      // 创建一个简单的错误提示音（低音调）

      // 方案1：使用系统提示音（如果可用）
      if (wx.playBackgroundAudio) {
        return null;
      }

      // 方案2：使用在线音效资源
      // return 'https://your-domain.com/sounds/wrong.mp3';

      // 方案3：使用本地音效文件
      return '/assets/sounds/wrong.wav';

    } catch (error) {
      console.log('生成错误音效数据失败:', error);
      return null;
    }
  },

  // 备用音效播放方案（使用系统API）
  playSystemSound: function(type) {
    try {
      if (type === 'correct') {
        // 播放成功音效 - 使用多次短震动模拟
        wx.vibrateShort({ type: 'light' });
        setTimeout(() => wx.vibrateShort({ type: 'light' }), 100);
        setTimeout(() => wx.vibrateShort({ type: 'light' }), 200);
      } else if (type === 'wrong') {
        // 播放错误音效 - 使用长震动模拟
        wx.vibrateShort({ type: 'heavy' });
        setTimeout(() => wx.vibrateShort({ type: 'heavy' }), 200);
      }
    } catch (error) {
      console.log('播放系统音效失败:', error);
    }
  },

  // 动态加载关卡数据
  async loadLevelData(shareId, levelId) {
    try {
      wx.showLoading({ title: '加载关卡数据...' });

      const result = await wx.cloud.callFunction({
        name: 'getShareTestLevel',
        data: {
          shareId: shareId,
          levelId: levelId
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        const levelData = result.result.data;
        console.log('动态加载关卡数据成功:', levelData);

        // 使用加载的关卡数据重新初始化测试
        this.initTestWithLevelData(levelData);
      } else {
        console.error('加载关卡数据失败:', result.result.message);
        wx.showToast({
          title: '加载关卡失败',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      wx.hideLoading();
      console.error('动态加载关卡数据异常:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 使用关卡数据初始化测试
  initTestWithLevelData(levelData) {
    const { testMode, timeLimit, perQuestionTime } = this.data.options || {};

    // 处理时间限制
    const isPracticeMode = timeLimit === 'unlimited';
    const questionTimeSeconds = isPracticeMode ? 0 : (perQuestionTime || 15);
    const timeLimitDisplay = isPracticeMode ? '不限时' : `${questionTimeSeconds}秒/题`;

    this.setData({
      testMode: testMode || 'en_to_cn',
      shareMode: 'share',
      shareId: levelData.shareId,
      perQuestionTime: questionTimeSeconds,
      currentQuestionTimeLeft: questionTimeSeconds,
      words: levelData.words,
      totalQuestions: levelData.words.length,
      libraryId: levelData.libraryId || 'shared',
      libraryName: levelData.libraryName || '分享测试',
      timeDisplay: isPracticeMode ? '练习模式' : `${questionTimeSeconds}秒`,
      timeLimitDisplay: timeLimitDisplay,
      isTestMode: !isPracticeMode,
      // 多关卡信息
      isMultiLevel: levelData.isMultiLevel || false,
      currentLevelId: levelData.levelId,
      totalLevels: levelData.totalLevels || 1,
      levelProgress: levelData.levelProgress,
      allLevels: levelData.allLevels || [],
      shareTestData: levelData.shareTestData // 保存完整的分享测试数据
    });

    // 开始测试
    this.startTest();
  }
});