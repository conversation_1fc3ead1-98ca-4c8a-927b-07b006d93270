// pages/phrasetest/phrasetest.js
const app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    plan: {
      dailyPhrases: 15,
      todayLearned: 0
    },
    planProgress: 0,
    todayLearned: 0,
    stats: {
      totalPhrases: 0,
      masteredPhrases: 0,
      correctRate: 0,
      totalTime: 0
    },
    records: [],
    canLearn: true,
    canReview: false,
    currentLibrary: {
      id: 'phrase_gaopin',
      name: '高频短语'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { shareMode } = options;
    
    // 检查是否为分享模式创建测试
    if (shareMode === 'create') {
      console.log('=== 短语测试分享模式 ===');
      
      // 存储分享模式状态
      this.setData({
        shareMode: true
      });
      
      // 延迟跳转到短语测试模式选择
      setTimeout(() => {
        this.showTestModeSelection();
      }, 500);
      return;
    }
    
    this.loadCurrentLibrary();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查是否有从词库页面选择的词库
    const app = getApp();
    if (app.globalData.selectedLibrary) {
      console.log('=== 短语检测从词库选择页面返回 ===', app.globalData.selectedLibrary);
      
      // 更新当前词库
      const selectedLibrary = app.globalData.selectedLibrary;
      this.setData({
        currentLibrary: {
          id: selectedLibrary.id,
          name: selectedLibrary.name,
          isCustom: selectedLibrary.isCustom || false,
          testOrder: selectedLibrary.testOrder || 'random'
        }
      });
      
      // 保存到本地缓存
      wx.setStorageSync('currentPhraseLibrary', this.data.currentLibrary);
      
      // 清除全局数据
      app.globalData.selectedLibrary = null;
      
      console.log('=== 短语词库更新完成 ===', this.data.currentLibrary);
    } else {
      // 页面显示时重新加载词库信息
      this.loadCurrentLibrary();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '墨词自习室 - 短语检测',
      path: '/pages/phrasetest/phrasetest',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const userInfo = await app.getUserInfo();
      this.setData({ userInfo });
    } catch (error) {
      console.error('加载用户信息失败：', error);
      wx.showToast({
        title: '加载用户信息失败',
        icon: 'none'
      });
    }
  },

  // 加载学习计划
  async loadPlan() {
    try {
      const db = wx.cloud.database();
      const userInfo = await app.getUserInfo();
      
      const settingsRes = await db.collection('users').doc(userInfo._id).get();
      const settings = settingsRes.data.settings;
      
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const recordsRes = await db.collection('learning_records')
        .where({
          userId: userInfo._id,
          learnTime: db.command.gte(today),
          type: 'phrase'
        })
        .count();
      
      const plan = {
        dailyPhrases: settings.dailyPhrases || 15,
        todayLearned: recordsRes.total
      };
      
      const planProgress = Math.min((plan.todayLearned / plan.dailyPhrases) * 100, 100);
      
      this.setData({
        plan,
        planProgress,
        todayLearned: plan.todayLearned
      });
    } catch (error) {
      console.error('加载学习计划失败：', error);
    }
  },

  // 加载学习统计
  async loadStats() {
    try {
      const db = wx.cloud.database();
      const userInfo = await app.getUserInfo();
      
      const statsRes = await db.collection('users').doc(userInfo._id).get();
      const stats = statsRes.data.stats;
      
      // 设置短语相关统计
      this.setData({ 
        stats: {
          totalPhrases: stats.totalPhrases || 0,
          masteredPhrases: stats.masteredPhrases || 0,
          correctRate: stats.phraseCorrectRate || 0,
          totalTime: stats.phraseTotalTime || 0
        }
      });
    } catch (error) {
      console.error('加载学习统计失败：', error);
    }
  },

  // 加载学习记录
  async loadRecords() {
    try {
      const db = wx.cloud.database();
      const userInfo = await app.getUserInfo();
      
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      
      const recordsRes = await db.collection('learning_records')
        .where({
          userId: userInfo._id,
          learnTime: db.command.gte(sevenDaysAgo),
          type: 'phrase'
        })
        .orderBy('learnTime', 'desc')
        .limit(7)
        .get();
      
      const records = recordsRes.data.map(record => {
        const date = new Date(record.learnTime);
        return {
          date: `${date.getMonth() + 1}月${date.getDate()}日`,
          phrases: record.phrases || record.words,
          time: record.time
        };
      });
      
      this.setData({ records });
    } catch (error) {
      console.error('加载学习记录失败：', error);
    }
  },

  // 检查学习状态
  async checkLearningStatus() {
    try {
      const db = wx.cloud.database();
      const userInfo = await app.getUserInfo();
      
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const recordsRes = await db.collection('learning_records')
        .where({
          userId: userInfo._id,
          learnTime: db.command.gte(today),
          type: 'phrase'
        })
        .get();
      
      const now = new Date();
      const reviewRes = await db.collection('learning_records')
        .where({
          userId: userInfo._id,
          nextReviewTime: db.command.lte(now),
          type: 'phrase'
        })
        .count();
      
      this.setData({
        canLearn: recordsRes.data.length < this.data.plan.dailyPhrases,
        canReview: reviewRes.total > 0
      });
    } catch (error) {
      console.error('检查学习状态失败：', error);
    }
  },

  // 开始学习
  startLearning() {
    if (!this.data.canLearn) {
      wx.showToast({
        title: '今日学习任务已完成',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/learn/learn?type=phrase'
    });
  },

  // 开始复习
  startReview() {
    if (!this.data.canReview) {
      wx.showToast({
        title: '暂无需要复习的短语',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/review/review?type=phrase'
    });
  },

  // 切换词库
  onSwitchLibrary() {
    console.log('=== 短语检测点击换书按钮 ===');
    
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
    
    console.log('=== 跳转到新的词库选择页面 ===');
    
    // 跳转到新的双列词库选择页面
    wx.navigateTo({
      url: '/pages/wordbank/library-selector/library-selector?returnTo=phrasetest&testType=phrase',
      success: () => {
        console.log('=== 成功跳转到词库选择页面 ===');
      },
      fail: (err) => {
        console.error('=== 跳转词库选择页面失败 ===:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 加载当前词库信息
   */
  async loadCurrentLibrary() {
    try {
      // 从缓存或用户设置中获取当前词库
      const userInfo = await app.getUserInfo();
      if (userInfo && userInfo.settings && userInfo.settings.currentPhraseLibrary) {
        this.setData({
          currentLibrary: userInfo.settings.currentPhraseLibrary
        });
      }
    } catch (error) {
      console.error('加载词库信息失败：', error);
    }
  },

  /**
   * 显示分享模式的测试模式选择
   */
  showTestModeSelection() {
    const modes = [
      { id: 'phrase_en2zh', name: '短语英译汉', icon: '🇨🇳' },
      { id: 'phrase_zh2en', name: '短语汉译英', icon: '🇺🇸' }
    ];

    const items = modes.map(mode => `${mode.icon} ${mode.name}`);

    wx.showActionSheet({
      itemList: items,
      success: (res) => {
        const selectedMode = modes[res.tapIndex];
        this.navigateToPhraseModeForShare(selectedMode);
      },
      fail: (res) => {
        console.log('用户取消选择');
        // 返回上一页
        wx.navigateBack();
      }
    });
  },

  /**
   * 跳转到短语测试模式（分享创建）
   */
  navigateToPhraseModeForShare(mode) {
    // 直接跳转到单词测试的模式选择页面，但标记为短语模式
    const actualTestMode = mode.id === 'phrase_en2zh' ? 'en_to_cn' : 'cn_to_en';

    // 跳转到词库选择页面，选择短语词库
    const url = `/pages/wordbank/wordlist/wordlist?mode=test&testMode=${actualTestMode}&shareMode=create&isPhrase=true&libraryId=phrase_gaopin`;

    wx.navigateTo({
      url: url,
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 选择检测模式
   */
  onModeSelect(e) {
    const mode = e.currentTarget.dataset.mode;

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'medium'
    });

    // 设置全局返回标志
    const app = getApp();
    app.globalData.returnToPhrasetest = true;

    // 显示加载提示
    wx.showLoading({
      title: '准备检测...',
      mask: true
    });

    // 根据不同模式跳转到对应页面
    setTimeout(() => {
      wx.hideLoading();

      // 判断是否为自定义词库
      const isCustom = this.data.currentLibrary.isCustom || false;
      const baseUrl = isCustom
        ? `/pages/wordbank/custom-wordlist/custom-wordlist?wordbankId=${this.data.currentLibrary.id}`
        : `/pages/wordbank/wordlist/wordlist?libraryId=${this.data.currentLibrary.id}`;

      switch (mode) {
        case 'en2zh':
          // 英译汉模式 - 跳转到词汇选择页面
          wx.navigateTo({
            url: isCustom
              ? `${baseUrl}&mode=select&returnTo=phrasetest&testMode=phrase_en2zh&isPhrase=true`
              : `${baseUrl}&mode=test&testMode=phrase_en2zh&isPhrase=true`
          });
          break;

        case 'zh2en':
          // 汉译英模式 - 跳转到词汇选择页面
          wx.navigateTo({
            url: isCustom
              ? `${baseUrl}&mode=select&returnTo=phrasetest&testMode=phrase_zh2en&isPhrase=true`
              : `${baseUrl}&mode=test&testMode=phrase_zh2en&isPhrase=true`
          });
          break;

        case 'confusing':
          // 易混淆短语辨析模式
          wx.navigateTo({
            url: `/pages/task/practice/practice?mode=confusing_phrase&library=${this.data.currentLibrary.id}`
          });
          break;

        case 'fill':
          // 填空练习模式
          wx.navigateTo({
            url: `/pages/task/practice/practice?mode=phrase_fill&library=${this.data.currentLibrary.id}`
          });
          break;

        default:
          wx.showToast({
            title: '该模式暂未开放',
            icon: 'none'
          });
      }
    }, 300);
  }
})