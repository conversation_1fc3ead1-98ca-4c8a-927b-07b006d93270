const app = getApp();

Page({
  data: {
    selectedMode: '', // practice 或 test
    selectedWords: [],
    libraryId: '',
    libraryName: '',
    showShareModal: false, // 控制分享弹窗显示
    currentShareData: null, // 分享数据
    shareId: '', // 分享ID
    
    // 练习模式配置
    practiceMode: {
      title: '练习模式',
      desc: '熟悉听写流程，可重复播放',
      icon: '📖',
      features: ['可重复播放', '不限时练习', '熟悉操作流程', '自主控制节奏']
    },
    
    // 测试模式配置
    testMode: {
      title: '测试模式',
      desc: '正式听写测试，检验掌握程度',
      icon: '✏️',
      features: ['正式测试环境', '记录测试成绩', '可分享给他人', '检验学习成果']
    },
    
    // 竞赛模式配置
    competitionMode: {
      title: '创建单词竞赛',
      desc: '创建听写竞赛，与他人一起挑战',
      icon: '🏆',
      features: ['创建公开竞赛', '实时排行榜', '7天自动删除', '支持分享转发']
    },
    
    // 听写设置
    playCount: 2, // 播放次数
    timeLimit: 15, // 每道题时间限制（秒）- 默认15秒
    wordOrder: 'original', // 🔧 新增：单词顺序设置 original: 保持原顺序, random: 随机顺序
    wordsPerGroup: 10, // 🔧 修改：每组词汇数量，默认10个
    
    playCountOptions: [
      { value: 1, label: '1次', desc: '挑战模式' },
      { value: 2, label: '2次', desc: '标准模式' },
      { value: 3, label: '3次', desc: '练习模式' }
    ],
    
    timeLimitOptions: [
      { value: 5, label: '5秒', desc: '快速挑战' },
      { value: 10, label: '10秒', desc: '标准速度' },
      { value: 15, label: '15秒', desc: '正常节奏' },
      { value: 20, label: '20秒', desc: '充裕时间' },
      { value: 30, label: '30秒', desc: '慢节奏' }
    ],
    
    // 🔧 新增：单词顺序选项
    wordOrderOptions: [
      { value: 'original', label: '保持原顺序', desc: '按照您选择的顺序' },
      { value: 'random', label: '随机顺序', desc: '打乱顺序增加挑战' }
    ],
    
    // 🔧 新增：每组词汇数量选项
    wordsPerGroupOptions: [
      { value: 5, label: '5个/组', desc: '快速练习' },
      { value: 10, label: '10个/组', desc: '推荐默认' },
      { value: 15, label: '15个/组', desc: '标准分组' },
      { value: 20, label: '20个/组', desc: '适中分组' },
      { value: 30, label: '30个/组', desc: '大分组' },
      { value: 50, label: '50个/组', desc: '挑战分组' }
    ],
    
    shareOption: 'self', // self 或 others
    
    // 有效期选择选项
    expireDays: 7,  // 默认7天
    expireDaysOptions: [
      { value: 7, label: '7天', desc: '短期分享' },
      { value: 30, label: '30天', desc: '一个月' },
      { value: 60, label: '60天', desc: '两个月' },
      { value: 90, label: '90天', desc: '三个月' },
      { value: 180, label: '180天', desc: '半年' },
      { value: 365, label: '1年', desc: '长期分享' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('=== 听写模式选择页面加载 ===');
    console.log('接收到的options:', options);
    
    // 启用分享功能
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    // 解析参数（类似于英译汉模式）
    if (options.testMode) {
      this.setData({
        testModeType: options.testMode,  // 使用不同的变量名，避免覆盖testMode配置
        total: parseInt(options.total) || 0
      });
    }
    
    this.loadSelectedWords();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('=== 听写模式选择页面显示 ===');

    // 检测分享成功（通过时间差判断）
    const { shareStartTime, shareId } = this.data;
    if (shareStartTime) {
      const now = Date.now();
      const timeDiff = now - shareStartTime;

      // 如果页面显示时间与分享开始时间差在合理范围内（1-10秒），认为是分享成功返回
      if (timeDiff > 1000 && timeDiff < 10000) {
        console.log('检测到分享成功返回，时间差:', timeDiff);

        // 记录分享成功
        if (shareId) {
          this.recordShareAction(shareId, 'wechat_share_success');
        }

        // 显示分享成功弹窗，提供返回和查看分享页选项
        this.showShareSuccessModal();

        // 清除分享开始时间
        this.setData({
          shareStartTime: null
        });
      }
    }
  },

  // 加载选择的词汇
  loadSelectedWords() {
    // 从全局数据中获取选择的词汇
    const app = getApp();
    const learningData = app.globalData.learningData;
    
    if (learningData && learningData.words) {
      this.setData({
        selectedWords: learningData.words,
        libraryId: learningData.libraryId,
        libraryName: learningData.libraryName
      });
      console.log('获取到词汇数量:', learningData.words.length);
    } else {
      wx.showToast({ title: '没有找到词汇数据', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 选择学习模式
  onModeSelect(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({ selectedMode: mode });
    
    // 添加触觉反馈
    wx.vibrateShort();
    
    console.log('选择听写模式:', mode);
  },

  // 选择播放次数
  onPlayCountSelect(e) {
    const count = parseInt(e.currentTarget.dataset.value);
    console.log('选择播放次数:', count);
    
    // 添加触觉反馈
    wx.vibrateShort();
    
    this.setData({ playCount: count });
  },

  // 选择时间限制
  onTimeLimitSelect(e) {
    const timeLimit = parseInt(e.currentTarget.dataset.value);
    console.log('选择时间限制:', timeLimit);

    // 添加触觉反馈
    wx.vibrateShort();

    this.setData({ timeLimit: timeLimit });
  },

  // 🔧 新增：选择单词顺序
  onWordOrderSelect(e) {
    const order = e.currentTarget.dataset.value;
    console.log('选择单词顺序:', order);
    
    // 添加触觉反馈
    wx.vibrateShort();
    
    this.setData({ wordOrder: order });
  },
  
  // 🔧 新增：选择每组词汇数量
  onWordsPerGroupSelect(e) {
    const count = parseInt(e.currentTarget.dataset.value);
    console.log('选择每组词汇数量:', count);
    
    // 添加触觉反馈
    wx.vibrateShort();
    
    this.setData({ wordsPerGroup: count });
  },

  // 选择分享选项
  onShareOptionSelect(e) {
    const option = e.currentTarget.dataset.option;
    this.setData({ shareOption: option });
  },

  // 选择有效期
  onExpireDaysSelect(e) {
    const days = e.currentTarget.dataset.value;
    this.setData({
      expireDays: days
    });
  },

  // 开始练习模式
  startPractice() {
    const { selectedWords, libraryId, libraryName } = this.data;
    
    if (!selectedWords || selectedWords.length === 0) {
      wx.showToast({ title: '没有找到词汇数据', icon: 'error' });
      return;
    }
    
    // 更新全局数据，添加练习模式标识
    const app = getApp();
    app.globalData.learningData = {
      words: selectedWords,
      mode: 'dictation',
      practiceMode: 'practice', // 练习模式
      libraryId: libraryId,
      libraryName: libraryName
    };

    // 跳转到听写练习页面
    wx.navigateTo({
      url: `/pages/spelling/practice/practice?mode=dictation&practiceMode=practice`,
      success: () => {
        console.log('跳转到听写练习页面成功');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  // 开始测试模式
  startTest() {
    const { selectedWords, libraryId, libraryName, playCount, timeLimit, shareOption } = this.data;
    
    if (!selectedWords || selectedWords.length === 0) {
      wx.showToast({ title: '没有找到词汇数据', icon: 'error' });
      return;
    }
    
    if (shareOption === 'self') {
      // 自己测试
      this.startSelfTest();
    } else if (shareOption === 'others') {
      // 分享给他人测试
      this.createShareTest();
    } else if (shareOption === 'competition') {
      // 创建竞赛
      this.startCompetition();
    }
  },

  // 开始自己测试
  startSelfTest() {
    const { selectedWords, libraryId, libraryName, playCount, timeLimit, wordOrder, wordsPerGroup } = this.data;
    
    // 🔧 修改分组逻辑：支持听写分组
    const totalWords = selectedWords.length;
    const actualWordsPerGroup = Math.min(wordsPerGroup, totalWords);
    const needsGrouping = totalWords > actualWordsPerGroup;
    let actualWords = selectedWords;
    
    const app = getApp();
    
    if (needsGrouping) {
      // 获取第一组词汇
      actualWords = selectedWords.slice(0, actualWordsPerGroup);
      
      // 保存完整的词汇列表和分组信息到全局
      app.globalData.testGroupingData = {
        allWords: selectedWords,
        wordsPerGroup: actualWordsPerGroup,
        currentGroup: 1,
        totalGroups: Math.ceil(totalWords / actualWordsPerGroup),
        testMode: 'dictation',
        dictationSettings: {
          playCount: playCount,
          timeLimit: timeLimit,
          wordOrder: wordOrder
        }
      };
      
      console.log('开始听写分组测试:', {
        totalWords: totalWords,
        selectedWordsPerGroup: wordsPerGroup,
        actualWordsPerGroup: actualWordsPerGroup,
        totalGroups: Math.ceil(totalWords / actualWordsPerGroup),
        firstGroupWords: actualWords.length
      });
    } else {
      console.log('听写词汇数量不足分组，创建单组测试:', {
        totalWords: totalWords,
        selectedWordsPerGroup: wordsPerGroup
      });
    }
    
    // 更新全局数据，添加测试模式设置
    app.globalData.learningData = {
      words: actualWords,
      mode: 'dictation',
      practiceMode: 'test', // 测试模式
      libraryId: libraryId,
      libraryName: libraryName,
      wordsPerGroup: actualWordsPerGroup,
      isGrouped: needsGrouping,
      currentGroup: needsGrouping ? 1 : null,
      totalGroups: needsGrouping ? Math.ceil(totalWords / actualWordsPerGroup) : null,
      dictationSettings: {
        playCount: playCount,
        timeLimit: timeLimit,
        wordOrder: wordOrder
      }
    };

    // 跳转到听写测试页面
    wx.navigateTo({
      url: `/pages/spelling/practice/practice?mode=dictation&practiceMode=test`,
      success: () => {
        console.log('跳转到听写测试页面成功');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  // 创建分享测试
  async createShareTest() {
    const { selectedWords, playCount, timeLimit, wordOrder, libraryId, libraryName } = this.data;
    
    wx.showLoading({ title: '创建分享测试...' });
    
    try {
      // 生成分享测试ID
      const shareId = 'dictation_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      
      // 获取当前用户信息
      const currentUser = wx.getStorageSync('userInfo') || {};
      
      // 创建分享测试数据
      const shareTestData = {
        shareId: shareId,
        testMode: 'dictation',
        words: selectedWords,
        libraryId: libraryId,
        libraryName: libraryName,
        dictationSettings: {
          playCount: playCount,
          timeLimit: timeLimit,
          wordOrder: wordOrder,
          wordsPerGroup: this.data.wordsPerGroup
        },
        createdBy: currentUser.nickName || '匿名用户',
        creatorInfo: {
          openid: currentUser.openid,
          nickName: currentUser.nickName,
          avatarUrl: currentUser.avatarUrl
        },
        createTime: Date.now(),
        visitors: [],
        results: []
      };
      
      // 保存到本地存储
      this.saveShareTestToLocal(shareTestData);

      // 保存到云端（必须成功）
      try {
        await this.saveShareTestToCloud(shareTestData);
        console.log('听写分享测试已成功保存到云端和本地');
      } catch (err) {
        console.error('云端保存失败:', err);
        wx.hideLoading();
        wx.showModal({
          title: '保存失败',
          content: '分享测试保存到云端失败，可能是网络问题。请检查网络连接后重试。',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }
      
      wx.hideLoading();
      
      // 使用底部弹出选项，不使用单独弹窗页面
      // 设置分享数据（为后续分享做准备）
      this.setData({
        currentShareData: {
          title: `🎧 听写测试 - ${selectedWords.length}个单词`,
          path: `pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`,
          imageUrl: '/assets/icons/logo.png'
        },
        shareId: shareId
      });
      
      // 确保分享记录存在
      this.ensureShareRecordExists(shareId);
      
      // 显示底部选项菜单
      wx.showActionSheet({
        itemList: ['复制测试ID', '分享给微信好友', '分享到企业微信'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              // 复制测试ID
              this.copyTestIdOnly();
              break;
            case 1:
              // 分享给微信好友 - 显示分享弹窗
              this.setData({
                showShareModal: true,
                isEnterpriseWeChatShare: false
              });
              break;
            case 2:
              // 分享到企业微信 - 显示分享弹窗
              this.setData({
                showShareModal: true,
                isEnterpriseWeChatShare: true
              });
              break;
          }
        }
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('创建分享测试失败:', error);
      wx.showToast({ title: '创建失败', icon: 'error' });
    }
  },

  // 保存分享测试到本地存储
  saveShareTestToLocal(shareTestData) {
    try {
      const shareTests = wx.getStorageSync('shareTests') || {};

      // 为了避免本地存储大小限制，只保存必要的元数据
      // 完整的词汇数据已经保存到云端，本地只需要保存基本信息
      const lightShareTestData = {
        shareId: shareTestData.shareId,
        testMode: shareTestData.testMode,
        libraryId: shareTestData.libraryId,
        libraryName: shareTestData.libraryName,
        wordsCount: shareTestData.words ? shareTestData.words.length : 0, // 只保存词汇数量
        dictationSettings: shareTestData.dictationSettings,
        createdBy: shareTestData.createdBy,
        creatorInfo: shareTestData.creatorInfo,
        createTime: shareTestData.createTime,
        lastShareTime: shareTestData.lastShareTime,
        // 不保存完整的words数组，以节省存储空间
        // words: shareTestData.words, // 注释掉这行
        visitors: [],
        results: []
      };

      shareTests[shareTestData.shareId] = lightShareTestData;
      wx.setStorageSync('shareTests', shareTests);
      console.log('听写分享测试已保存到本地:', shareTestData.shareId, '词汇数量:', lightShareTestData.wordsCount);
    } catch (error) {
      console.error('保存到本地存储失败:', error);
      // 如果本地存储失败，不要抛出错误，因为云端保存是主要的
      // 本地存储主要用于分享记录和统计，不是必需的
      console.warn('本地存储失败，但云端保存成功，分享功能仍可正常使用');
    }
  },

  // 保存分享测试到云端
  async saveShareTestToCloud(shareTestData) {
    try {
      // 获取学习数据以确定是否为乱序
      const app = getApp();
      const learningData = app.globalData.learningData;

      // 超级优化：判断是否使用基于索引的存储策略
      const isSystemLibrary = shareTestData.libraryId &&
                              !shareTestData.libraryId.includes('custom') &&
                              !shareTestData.libraryId.includes('mistake');
      const isLargeLibrary = shareTestData.words && shareTestData.words.length >= 100;
      const useIndexBasedStorage = isSystemLibrary && isLargeLibrary;

      const result = await wx.cloud.callFunction({
        name: 'createShareTest',
        data: {
          shareId: shareTestData.shareId,  // 传递shareId
          testType: shareTestData.testMode,
          // 超级优化：基于索引存储时只传递必要信息
          words: useIndexBasedStorage ?
            [{
              _id: shareTestData.words[0]._id,
              words: shareTestData.words[0].words,
              totalCount: shareTestData.words.length
            }] :
            shareTestData.words,
          libraryId: shareTestData.libraryId,
          libraryName: shareTestData.libraryName,
          // 超级优化：传递乱序信息，支持基于索引的存储
          isRandomOrder: learningData ? (learningData.isRandom || false) : false,
          settings: shareTestData.dictationSettings,
          expireDays: this.data.expireDays,
          wordsPerGroup: this.data.wordsPerGroup
        }
      });
      
      if (result.result.success) {
        console.log('分享测试已保存到云端:', shareTestData.shareId);
      } else {
        throw new Error(result.result.message || '云端保存失败');
      }
    } catch (error) {
      console.error('保存到云端失败:', error);
      throw error;
    }
  },

  // 显示分享选项
  showShareOptions(options) {
    const { title, content, shareId, shareData } = options;
    
    wx.showActionSheet({
      itemList: ['复制测试ID', '分享到微信', '查看管理'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 复制测试ID
            this.copyTestIdOnly({ currentTarget: { dataset: { shareId } } });
            break;
          case 1:
            // 分享到微信
            this.shareToWeChat(shareData);
            break;
          case 2:
            // 查看管理
            this.goToShareManagement();
            break;
        }
      }
    });
  },

  // 复制分享链接
  copyShareLink(shareId) {
    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);
    
    // 生成分享文本
    const shareText = `墨词自习室听写测试邀请\n\n测试ID: ${shareId}\n\n请在"墨词自习室"小程序中，进入"我的"->"收到的分享"，输入测试ID参与测试。`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        // 记录分享操作
        this.recordShareAction(shareId, 'copy_link');
        
        wx.showModal({
          title: '分享内容已复制',
          content: '已复制分享信息到剪贴板。\n\n对方可以：\n1. 在小程序搜索"墨词自习室"\n2. 进入"我的"->"收到的分享"\n3. 输入测试ID进行测试',
          showCancel: false,
          confirmText: '知道了'
        });
        console.log('分享内容已复制:', shareText);
      },
      fail: () => {
        wx.showToast({ title: '复制失败', icon: 'error' });
      }
    });
  },

  // 分享到微信
  shareToWeChat(shareData) {
    const { testMode, testName, shareId } = shareData;
    
    console.log('开始分享到微信:', shareData);
    
    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);
    
    // 设置当前分享数据
    this.setData({
      currentShareData: {
        title: `🎧 ${testName}`,
        path: `pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`,
        imageUrl: '/assets/icons/logo.png'
      },
      shareId: shareId,
      showShareModal: true
    });

    console.log('分享数据设置完成，显示分享弹窗');
  },

  // 关闭分享弹窗
  closeShareModal() {
    console.log('关闭分享弹窗');
    this.setData({
      showShareModal: false
    });
  },

  // 分享按钮点击
  onShareButtonTap() {
    // 记录分享操作
    const shareId = this.data.shareId;
    if (shareId) {
      this.recordShareAction(shareId, 'wechat_share');
    }

    // 设置分享开始标识和特殊标记
    const shareTimestamp = Date.now();
    this.setData({
      showShareModal: false,
      shareStartTime: shareTimestamp
    });

    // 在本地存储中设置标识，用于检测"查看分享页"点击
    wx.setStorageSync('shareButtonClickTime', shareTimestamp);
    wx.setStorageSync('currentShareId', shareId);

    // 这个函数会在用户点击分享按钮时触发onShareAppMessage
    console.log('分享按钮被点击');
  },

  // 复制测试ID
  copyTestIdOnly(e) {
    const shareId = e?.currentTarget?.dataset?.shareId || this.data.shareId;
    
    if (!shareId) {
      wx.showToast({ title: '测试ID不存在', icon: 'error' });
      return;
    }
    
    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);
    
    wx.setClipboardData({
      data: shareId,
      success: () => {
        // 记录分享操作
        this.recordShareAction(shareId, 'copy_test_id');
        
        wx.showToast({ 
          title: '测试ID已复制', 
          icon: 'success' 
        });
        // 关闭分享弹窗
        this.setData({
          showShareModal: false
        });
        setTimeout(() => {
          wx.showModal({
            title: '测试ID已复制',
            content: '测试ID已复制到剪贴板。\n\n对方可以在"我的"->"收到的分享"中输入这个ID来参与测试。',
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1500);
      },
      fail: () => {
        wx.showToast({ title: '复制失败', icon: 'error' });
      }
    });
  },

  // 分享到企业微信
  shareToEnterpriseWeChat(e) {
    const shareId = this.data.shareId;
    const currentShareData = this.data.currentShareData;
    
    if (!shareId || !currentShareData) {
      wx.showToast({ title: '分享数据不完整', icon: 'error' });
      return;
    }
    
    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);
    
    // 记录分享操作
    this.recordShareAction(shareId, 'share_to_enterprise_wechat');
    
    // 设置企业微信分享标识
    this.setData({
      isEnterpriseWeChatShare: true
    });
    
    // 提示用户选择企业微信联系人
    wx.showToast({
      title: '即将打开分享选择',
      icon: 'none',
      duration: 1500
    });
    
    // 关闭分享弹窗
    this.setData({
      showShareModal: false
    });
    
    // 延迟一点时间，然后触发分享
    setTimeout(() => {
      // 这将触发 onShareAppMessage 方法
      wx.updateShareMenu({
        withShareTicket: true,
        success: () => {
          // 模拟点击分享按钮的效果
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage']
          });
        }
      });
    }, 1600);
  },

  // 复制完整分享信息（保留原有功能）
  copyCompleteShareInfo(e) {
    const shareId = e?.currentTarget?.dataset?.shareId || this.data.shareId;
    const testName = e?.currentTarget?.dataset?.testName || this.data.currentShareData?.title;
    
    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);
    
    const shareText = `🎧 墨词自习室听写测试邀请\n\n📝 ${testName}\n🆔 测试ID: ${shareId}\n\n📲 参与方式：\n1. 打开"墨词自习室"小程序\n2. 进入"我的"->"收到的分享"\n3. 输入测试ID开始测试\n\n快来挑战吧！`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        // 记录分享操作
        this.recordShareAction(shareId, 'copy_complete_info');
        
        wx.showToast({ 
          title: '分享信息已复制', 
          icon: 'success' 
        });
        // 关闭分享弹窗
        this.setData({
          showShareModal: false
        });
        setTimeout(() => {
          wx.showModal({
            title: '发送给朋友',
            content: '完整的分享信息已复制，可以发送到微信群聊或好友对话中。\n\n朋友看到后按照说明即可参与测试。',
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1500);
      },
      fail: () => {
        wx.showToast({ title: '复制失败', icon: 'error' });
      }
    });
  },

  // 跳转到分享管理页面
  goToShareManagement() {
    wx.navigateTo({
      url: '/pages/profile/share/share',
      success: () => {
        console.log('跳转到分享管理页面成功');
      },
      fail: (err) => {
        console.error('跳转到分享管理页面失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 开始创建竞赛
  async startCompetition() {
    const { selectedWords, libraryId, libraryName } = this.data;
    
    // 检查用户登录状态
    const app = getApp();
    if (!app.isLoggedIn()) {
      wx.showModal({
        title: '需要登录',
        content: '创建竞赛需要登录，是否立即登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    if (!selectedWords || selectedWords.length === 0) {
      wx.showToast({ title: '没有找到词汇数据', icon: 'error' });
      return;
    }

    // 使用自定义输入组件
    this.selectComponent('#competitionInput').showDialog();
  },

  // 处理竞赛名称输入确认
  async onCompetitionInputConfirm(e) {
    const app = getApp();
    const learningData = app.globalData.learningData;

    if (!learningData || !learningData.words) {
      wx.showToast({ title: '数据加载失败', icon: 'error' });
      return;
    }

    // 获取输入的竞赛名称
    const competitionName = e.detail.value.trim() || '我的听写挑战';
    
    wx.showLoading({
      title: '创建中...',
      mask: true
    });

    console.log('创建听写竞赛:', { 
      name: competitionName, 
      mode: 'dictation', 
      wordsCount: learningData.words.length,
      libraryId: learningData.libraryId,
      libraryName: learningData.libraryName
    });

    try {
      // 超级优化：判断是否使用基于索引的存储策略
      const isSystemLibrary = learningData.libraryId &&
                              !learningData.libraryId.includes('custom') &&
                              !learningData.libraryId.includes('mistake');
      const isLargeLibrary = learningData.words && learningData.words.length >= 100;
      const useIndexBasedStorage = isSystemLibrary && isLargeLibrary;

      // 调用云函数创建竞赛
      const result = await wx.cloud.callFunction({
        name: 'createCompetition',
        data: {
          name: competitionName,
          mode: 'dictation',
          // 超级优化：基于索引存储时只传递必要信息
          words: useIndexBasedStorage ?
            [{
              _id: learningData.words[0]._id,
              words: learningData.words[0].words,
              totalCount: learningData.words.length
            }] :
            learningData.words,
          libraryId: learningData.libraryId,
          libraryName: learningData.libraryName,
          // 传递乱序信息，支持基于索引的存储
          isRandomOrder: learningData.isRandom || false,
          wordsPerGroup: this.data.wordsPerGroup,
          dictationSettings: {
            playCount: this.data.playCount,
            timeLimit: this.data.timeLimit,
            wordOrder: this.data.wordOrder
          }
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        wx.showModal({
          title: '创建成功',
          content: `竞赛"${competitionName}"创建成功！现在可以分享给好友参与了。`,
          showCancel: true,
          cancelText: '稍后分享',
          confirmText: '立即分享',
          success: (shareRes) => {
            // 设置全局标记以便高亮显示新创建的竞赛
            const app = getApp();
            app.globalData.highlightCompetitionId = result.result.competitionId;
            
            if (shareRes.confirm) {
              // 跳转到竞赛页面进行分享
              wx.navigateTo({
                url: `/pages/competition/competition?mode=dictation&highlightId=${result.result.competitionId}`
              });
            } else {
              // 跳转到竞赛列表
              wx.navigateTo({
                url: '/pages/competition/competition?mode=dictation'
              });
            }
          }
        });
      } else {
        throw new Error(result.result.message || '创建失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('创建竞赛失败:', error);
      wx.showToast({
        title: '创建失败，请重试',
        icon: 'none'
      });
    }
  },

  // 处理竞赛名称输入取消
  onCompetitionInputCancel() {
    console.log('取消创建竞赛');
  },

  // 返回上一页
  onBack() {
    wx.navigateBack();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const { currentShareData, isEnterpriseWeChatShare, shareId } = this.data;
    
    if (currentShareData) {
      console.log('分享数据:', currentShareData);
      
      // 准备分享内容
      let shareTitle = currentShareData.title;
      let sharePath = currentShareData.path;
      
      // 如果是企业微信分享，添加特殊标识
      if (isEnterpriseWeChatShare) {
        shareTitle = `💼 ${shareTitle} (企业微信分享)`;
        // 重置企业微信分享标识
        this.setData({ isEnterpriseWeChatShare: false });
        
        // 提示用户这是企业微信分享
        setTimeout(() => {
          wx.showModal({
            title: '企业微信分享提示',
            content: `分享链接已生成！\n\n📋 测试ID: ${shareId}\n\n💡 提醒收到分享的同事：\n1. 点击分享链接打开小程序\n2. 或在小程序"我的"->"收到的分享"中输入测试ID\n\n企业微信用户也可以直接点击链接参与测试！`,
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1000);
      }
      
      return {
        title: shareTitle,
        path: sharePath,
        imageUrl: currentShareData.imageUrl
      };
    }
    
    return {
      title: '墨词自习室 - 英语学习好帮手',
      path: '/pages/index/index',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 确保分享记录存在
  async ensureShareRecordExists(shareId) {
    try {
      const shareTests = wx.getStorageSync('shareTests') || {};
      if (!shareTests[shareId]) {
        console.warn('分享记录不存在，尝试重新创建:', shareId);
        // 如果记录不存在，尝试从当前数据重新创建
        const currentUser = wx.getStorageSync('userInfo') || {};
        const { selectedWords, playCount, timeLimit, libraryId, libraryName } = this.data;

        const shareTestData = {
          shareId: shareId,
          testMode: 'dictation',
          words: selectedWords,
          libraryId: libraryId,
          libraryName: libraryName,
          dictationSettings: {
            playCount: playCount,
            timeLimit: timeLimit
          },
          createdBy: currentUser.nickName || '匿名用户',
          creatorInfo: {
            openid: currentUser.openid,
            nickName: currentUser.nickName,
            avatarUrl: currentUser.avatarUrl
          },
          createTime: Date.now(),
          visitors: [],
          results: []
        };

        this.saveShareTestToLocal(shareTestData);

        // 同时保存到云端
        try {
          await this.saveShareTestToCloud(shareTestData);
          console.log('重新创建的分享记录已保存到云端');
        } catch (err) {
          console.warn('重新创建的分享记录云端保存失败:', err);
        }
      }
    } catch (error) {
      console.error('确保分享记录存在时出错:', error);
    }
  },

  // 记录分享操作
  recordShareAction(shareId, actionType) {
    try {
      const shareTests = wx.getStorageSync('shareTests') || {};
      if (shareTests[shareId]) {
        // 记录操作历史
        if (!shareTests[shareId].shareActions) {
          shareTests[shareId].shareActions = [];
        }

        shareTests[shareId].shareActions.push({
          type: actionType,
          timestamp: Date.now()
        });

        // 更新最后分享时间
        shareTests[shareId].lastShareTime = Date.now();

        wx.setStorageSync('shareTests', shareTests);
        console.log('分享操作已记录:', { shareId, actionType });
      }
    } catch (error) {
      console.error('记录分享操作失败:', error);
    }
  },

  /**
   * 显示分享成功弹窗
   */
  showShareSuccessModal() {
    wx.showModal({
      title: '分享成功',
      content: '听写测试已成功分享给好友！',
      cancelText: '返回',
      confirmText: '查看分享页',
      success: (res) => {
        if (res.confirm) {
          // 用户点击"查看分享页"
          wx.navigateTo({
            url: '/pages/profile/share/share',
            success: () => {
              console.log('跳转到我的分享页面成功');
            },
            fail: (err) => {
              console.error('跳转失败:', err);
              wx.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        } else if (res.cancel) {
          // 用户点击"返回"，不做任何操作，保持在当前页面
          console.log('用户选择返回当前页面');
        }
      }
    });
  }
});