const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 词库ID到集合名称的映射表
const COLLECTION_NAMES = {
  // 中考词汇
  'zhongkao_1600': 'words_zhongkao',

  // 大学词汇
  'college_cet4': 'words_siji',
  'college_cet6': 'words_liuji',

  // 高考大纲词汇
  'gaokao_3500': 'words_3500',
  'gaokao_3500_luan': 'words_3500_luan',
  'gaokao_weikeduo': 'words_weikeduo',

  // 常用短语
  'phrase_gaopin': 'phrase_gaopin',
  'phrase_hunxiao': 'phrase_hunxiao',

  // 题型专项词汇
  'special_wusan_gaopin_beijing': 'words_bjwusan',
  'special_yuedu_tongyong': 'words_reading_tongyong',
  'special_yuedu_beijing': 'words_reading_beijing',
  'special_wanxing_gaopin_beijing': 'words_wanxing_gaopin_beijing',
  'special_wanxing_shucishengyi_beijing': 'words_wanxing_shucishengyi_beijing',

  // 其他词汇
  'other_buguize': 'words_buguize',
  'other_xingjinci': 'words_xingjinci',
  'other_shucishengyi': 'words_shucishengyi_tongyong'
};

// 分批加载词库数据的辅助函数
async function loadAllWordsFromLibrary(libraryId) {
  console.log('开始分批加载词库:', libraryId);

  // 获取实际的集合名称
  const collectionName = COLLECTION_NAMES[libraryId] || libraryId;
  console.log('词库ID映射:', libraryId, '->', collectionName);

  // 先获取总数
  const countResult = await db.collection(collectionName).count();
  const totalCount = countResult.total;

  console.log(`词库总数: ${totalCount}`);

  if (totalCount === 0) {
    return [];
  }

  const batchSize = 1000; // 每批1000个
  const batches = Math.ceil(totalCount / batchSize);
  const allWords = [];

  for (let i = 0; i < batches; i++) {
    const skip = i * batchSize;
    console.log(`加载第${i + 1}批，跳过${skip}个`);

    const batchResult = await db.collection(collectionName)
      .skip(skip)
      .limit(batchSize)
      .get();

    allWords.push(...batchResult.data);
    console.log(`第${i + 1}批加载完成，获取${batchResult.data.length}个词汇`);
  }

  console.log(`词库加载完成，总共${allWords.length}个词汇`);
  return allWords;
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  
  try {
    const { shareId, levelId } = event;
    
    if (!shareId || !levelId) {
      return {
        success: false,
        message: '缺少必要参数'
      };
    }
    
    console.log(`获取分享测试关卡数据: ${shareId}, 关卡: ${levelId}`);
    
    // 从数据库获取分享测试数据
    const result = await db.collection('shareTests')
      .where({
        shareId: shareId
      })
      .get();
    
    if (!result.data || result.data.length === 0) {
      return {
        success: false,
        message: '分享测试不存在'
      };
    }
    
    let shareTestData = result.data[0];
    
    // 检查是否过期
    if (shareTestData.expireTime) {
      const now = new Date();
      const expireTime = new Date(shareTestData.expireTime);
      if (now > expireTime) {
        return {
          success: false,
          message: '分享测试已过期'
        };
      }
    }
    
    // 如果需要动态加载词汇数据
    if (shareTestData.isOptimizedStorage && !shareTestData.words) {
      const { storageStrategy, wordSelection, libraryId } = shareTestData;
      console.log('动态加载词汇数据...');
      
      let loadedWords = [];
      
      if (storageStrategy === 'index_based' && wordSelection) {
        // 基于索引的超级优化存储
        console.log('使用索引存储策略，词汇数量:', wordSelection.totalWords);

        // 分批获取完整词库数据（避免大型词库查询超时）
        const allWords = await loadAllWordsFromLibrary(libraryId);

        if (allWords.length === 0) {
          throw new Error('词库数据为空');
        }

        // 根据索引序列重新排序词汇
        const wordIndexes = wordSelection.wordIndexes || [];
        loadedWords = wordIndexes.map(index => allWords[index]).filter(word => word);

        console.log(`索引存储加载成功: ${loadedWords.length}/${wordIndexes.length} 个词汇`);

      } else if (storageStrategy === 'reference' && wordSelection?.selectionType === 'full') {
        // 完整词库引用：分批加载整个词库
        loadedWords = await loadAllWordsFromLibrary(libraryId);

      } else if (storageStrategy === 'hybrid' && wordSelection?.selectionType === 'partial') {
        // 部分词汇引用
        const wordIds = wordSelection.wordIds || [];
        
        if (wordIds.length > 0) {
          const batchSize = 20;
          const batches = [];
          
          for (let i = 0; i < wordIds.length; i += batchSize) {
            const batchIds = wordIds.slice(i, i + batchSize);
            const batchResult = await db.collection(libraryId)
              .where({
                _id: db.command.in(batchIds)
              })
              .get();
            
            if (batchResult.data) {
              batches.push(...batchResult.data);
            }
          }
          
          loadedWords = wordIds.map(id => 
            batches.find(word => word._id === id)
          ).filter(word => word);
        }
      }
      
      if (loadedWords.length > 0) {
        shareTestData.words = loadedWords;
      } else {
        return {
          success: false,
          message: '词汇数据不存在'
        };
      }
    }
    
    // 生成指定关卡的数据
    if (!shareTestData.words || shareTestData.words.length === 0) {
      return {
        success: false,
        message: '词汇数据为空'
      };
    }
    
    const wordsPerGroup = shareTestData.wordsPerGroup || 20;
    const totalLevels = shareTestData.totalLevels || 1;
    const requestedLevelId = parseInt(levelId);
    
    if (requestedLevelId < 1 || requestedLevelId > totalLevels) {
      return {
        success: false,
        message: '关卡ID无效'
      };
    }
    
    // 生成指定关卡的词汇数据
    const start = (requestedLevelId - 1) * wordsPerGroup;
    const end = Math.min(start + wordsPerGroup, shareTestData.words.length);
    const levelWords = shareTestData.words.slice(start, end);
    
    // 检查用户权限（是否解锁了该关卡）
    const currentUser = wxContext.OPENID;
    const userProgress = shareTestData.levelProgress ? shareTestData.levelProgress[currentUser] : null;
    const currentLevel = userProgress ? userProgress.currentLevel : 1;
    const isUnlocked = requestedLevelId <= currentLevel;
    
    if (!isUnlocked) {
      return {
        success: false,
        message: '该关卡尚未解锁'
      };
    }
    
    console.log(`成功生成关卡 ${requestedLevelId} 数据，包含 ${levelWords.length} 个词汇`);
    
    return {
      success: true,
      data: {
        levelId: requestedLevelId,
        words: levelWords,
        isUnlocked: isUnlocked,
        totalWords: levelWords.length,
        // 基本测试信息
        shareId: shareTestData.shareId,
        testType: shareTestData.testType,
        libraryId: shareTestData.libraryId,
        libraryName: shareTestData.libraryName,
        settings: shareTestData.settings,
        // 关卡信息
        totalLevels: totalLevels,
        wordsPerGroup: wordsPerGroup,
        // 用户进度
        userProgress: userProgress
      }
    };
    
  } catch (error) {
    console.error('获取分享测试关卡失败:', error);
    return {
      success: false,
      message: '获取关卡数据失败',
      error: error
    };
  }
};
