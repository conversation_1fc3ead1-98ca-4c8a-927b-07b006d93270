<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">音标专练</text>
      <text class="page-subtitle">掌握标准发音技巧</text>
    </view>
  </view>

  <!-- 选择模式切换 -->
  <view class="mode-section">
    <view class="mode-title">选择模式</view>
    <radio-group class="mode-radio" bindchange="onSelectionModeChange">
      <label class="mode-option">
        <radio value="single" checked="{{selectionMode === 'single'}}" />
        <text class="mode-text">单选</text>
      </label>
      <label class="mode-option">
        <radio value="multiple" checked="{{selectionMode === 'multiple'}}" />
        <text class="mode-text">多选</text>
      </label>
    </radio-group>
  </view>

  <!-- 音标选择区域 -->
  <view class="selection-section">
    <view class="selection-header">
      <text class="section-title">音标选择</text>
      <view class="selection-info">
        <text class="info-text">已选择 {{selectedPhonetics.length}} 个音标</text>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tab-bar">
      <view 
        class="tab-item {{activeTab === 'vowels' ? 'active' : ''}}" 
        bindtap="onTabTap" 
        data-tab="vowels"
      >
        <text class="tab-text">元音 ({{phonetics.vowels.length}})</text>
      </view>
      <view 
        class="tab-item {{activeTab === 'consonants' ? 'active' : ''}}" 
        bindtap="onTabTap" 
        data-tab="consonants"
      >
        <text class="tab-text">辅音 ({{phonetics.consonants.length}})</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="selection-actions">
      <view class="action-btn secondary" bindtap="onClearSelection">
        <text class="action-icon">🗑️</text>
        <text class="action-text">清空选择</text>
      </view>
      <view class="action-btn secondary" bindtap="onSelectAll" wx:if="{{selectionMode === 'multiple'}}">
        <text class="action-icon">✅</text>
        <text class="action-text">全选</text>
      </view>
    </view>

    <!-- 音标网格 - 元音 -->
    <view class="phonetic-grid" wx:if="{{activeTab === 'vowels'}}">
      <view 
        class="phonetic-item {{item.selected ? 'selected' : ''}}"
        wx:for="{{displayPhonetics.vowels}}" 
        wx:key="symbol"
        bindtap="onPhoneticTap"
        data-phonetic="{{item}}"
      >
        <view class="phonetic-symbol">{{item.symbol}}</view>
        <view class="phonetic-name">{{item.name}}</view>
        <view class="phonetic-example">{{item.example}}</view>
        <view class="selected-indicator" wx:if="{{item.selected}}">
          <text class="indicator-icon">✓</text>
        </view>
      </view>
    </view>

    <!-- 音标网格 - 辅音 -->
    <view class="phonetic-grid" wx:if="{{activeTab === 'consonants'}}">
      <view 
        class="phonetic-item {{item.selected ? 'selected' : ''}}"
        wx:for="{{displayPhonetics.consonants}}" 
        wx:key="symbol"
        bindtap="onPhoneticTap"
        data-phonetic="{{item}}"
      >
        <view class="phonetic-symbol">{{item.symbol}}</view>
        <view class="phonetic-name">{{item.name}}</view>
        <view class="phonetic-example">{{item.example}}</view>
        <view class="selected-indicator" wx:if="{{item.selected}}">
          <text class="indicator-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 已选择的音标显示 -->
  <view class="selected-section" wx:if="{{selectedPhonetics.length > 0}}">
    <view class="selected-header">
      <text class="section-title">已选择的音标</text>
    </view>
    <view class="selected-list">
      <view class="selected-item" wx:for="{{selectedPhonetics}}" wx:key="symbol">
        <text class="selected-symbol">{{item.symbol}}</text>
        <text class="selected-name">{{item.name}}</text>
        <text class="selected-example">{{item.example}}</text>
      </view>
    </view>
  </view>

  <!-- 生成按钮 -->
  <view class="generate-section">
    <button 
      class="generate-btn {{selectedPhonetics.length === 0 ? 'disabled' : ''}}"
      bindtap="onGenerateContent"
      disabled="{{selectedPhonetics.length === 0 || isProcessing}}"
    >
      <text class="btn-icon" wx:if="{{!isProcessing}}">🤖</text>
      <text class="btn-icon loading" wx:if="{{isProcessing}}">⏳</text>
      <text class="btn-text">{{isProcessing ? 'AI生成中...' : '开始生成学习指导'}}</text>
    </button>
  </view>

  <!-- 结果显示区域 -->
  <view class="result-section" wx:if="{{showResult}}">
    <view class="result-header">
      <text class="section-title">学习指导</text>
      <view class="result-actions">
        <view class="action-btn small" bindtap="onCopyContent">
          <text class="action-icon">📋</text>
          <text class="action-text">复制</text>
        </view>
        <view class="action-btn small" bindtap="onRegenerate">
          <text class="action-icon">🔄</text>
          <text class="action-text">重新生成</text>
        </view>
        <view class="action-btn small" bindtap="onClearResult">
          <text class="action-icon">🗑️</text>
          <text class="action-text">清空</text>
        </view>
      </view>
    </view>
    
    <view class="result-content">
      <text class="result-text">{{result}}</text>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="help-section" wx:if="{{!showResult}}">
    <view class="help-content">
      <view class="help-title">🎯 如何使用音标专练</view>
      <view class="help-list">
        <view class="help-item">
          <text class="help-number">1</text>
          <text class="help-text">选择单选或多选模式</text>
        </view>
        <view class="help-item">
          <text class="help-number">2</text>
          <text class="help-text">在元音或辅音标签页中选择音标</text>
        </view>
        <view class="help-item">
          <text class="help-number">3</text>
          <text class="help-text">点击"开始生成学习指导"获取AI指导</text>
        </view>
        <view class="help-item">
          <text class="help-number">4</text>
          <text class="help-text">学习发音要点、技巧和练习方法</text>
        </view>
      </view>
    </view>
  </view>
</view> 