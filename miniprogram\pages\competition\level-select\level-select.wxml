<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <view class="header-top">
        <view class="back-btn" bindtap="onBackToCompetition">
          <text class="back-icon">←</text>
        </view>
        <view class="header-title">
          <text class="competition-name">{{competitionName}}</text>
          <text class="competition-mode">{{modeName}}多关卡竞赛</text>
        </view>
        <view class="header-action">
          <view class="ranking-btn" bindtap="onViewOverallRanking">
            <text class="ranking-icon">🏆</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 竞赛统计 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{totalLevels}}</text>
        <text class="stat-label">总关卡</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{totalWords}}</text>
        <text class="stat-label">总词汇</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{totalParticipants}}</text>
        <text class="stat-label">参与人数</text>
      </view>
      <view class="stat-item" wx:if="{{averageScore > 0}}">
        <text class="stat-number">{{averageScore}}</text>
        <text class="stat-label">平均分</text>
      </view>
    </view>
  </view>

  <!-- 关卡列表 -->
  <view class="levels-section">
    <!-- 加载中 -->
    <view class="loading" wx:if="{{loading}}">
      <view class="loading-icon"></view>
      <text class="loading-text">加载关卡中...</text>
    </view>

    <!-- 关卡卡片 -->
    <view class="level-card {{item.locked ? 'locked' : ''}} {{item.completed ? 'completed' : ''}}" 
          wx:for="{{levels}}" 
          wx:key="id"
          bindtap="{{item.locked ? '' : 'onLevelTap'}}"
          data-id="{{item.id}}">
      
      <view class="level-header">
        <view class="level-info">
          <view class="level-title">
            <text class="level-number">第{{item.levelNumber}}关</text>
            <view class="level-badge">
              <text class="badge-text">{{item.wordCount}}词</text>
            </view>
          </view>
          <text class="level-name">{{item.name}}</text>
        </view>
        <view class="level-stats">
          <view class="stat-row">
            <text class="stat-icon">👥</text>
            <text class="stat-text">{{item.participants}}人</text>
          </view>
          <view class="stat-row" wx:if="{{item.topScore > 0}}">
            <text class="stat-icon">🏅</text>
            <text class="stat-text">{{item.topScore}}分</text>
          </view>
        </view>
      </view>

      <view class="level-content">
        <view class="progress-section" wx:if="{{item.participants > 0}}">
          <view class="progress-info">
            <text class="progress-label">参与进度</text>
            <text class="progress-text">{{item.completionRate || 0}}%</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" 
                  style="width: {{item.completionRate || 0}}%"></view>
          </view>
        </view>

        <view class="level-action">
          <!-- 已锁定状态 -->
          <view class="action-btn locked-btn" wx:if="{{item.locked}}">
            <text class="action-icon">🔒</text>
            <text class="action-text">需完成第{{item.levelNumber - 1}}关</text>
          </view>
          <!-- 已完成状态 -->
          <view class="action-btn completed-btn" wx:elif="{{item.completed}}">
            <text class="action-icon">✅</text>
            <text class="action-text">已完成 - 再次挑战</text>
          </view>
          <!-- 可挑战状态 -->
          <view class="action-btn challenge-btn" wx:else>
            <text class="action-icon">🎯</text>
            <text class="action-text">开始挑战</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && levels.length === 0}}">
      <text class="empty-icon">📝</text>
      <text class="empty-text">暂无关卡数据</text>
      <text class="empty-tip">请返回重新加载</text>
    </view>

    <!-- 分页控件 -->
    <view class="pagination" wx:if="{{!loading && totalPages > 1}}">
      <view class="pagination-info">
        <text class="page-text">第{{currentPage}}页 / 共{{totalPages}}页</text>
        <text class="total-text">共{{totalLevels}}关</text>
      </view>

      <view class="pagination-controls">
        <!-- 上一页 -->
        <view class="page-btn {{currentPage <= 1 ? 'disabled' : ''}}"
              bindtap="{{currentPage <= 1 ? '' : 'onPrevPage'}}">
          <text class="page-btn-text">上一页</text>
        </view>

        <!-- 页码按钮 -->
        <view class="page-numbers">
          <view class="page-number {{currentPage === item ? 'active' : ''}} {{item === '...' ? 'ellipsis' : ''}}"
                wx:for="{{pageNumbers}}"
                wx:key="*this"
                bindtap="{{item === '...' ? '' : 'onGoToPage'}}"
                data-page="{{item}}">
            {{item}}
          </view>

          <!-- 自定义页码输入按钮 -->
          <view class="page-input-btn" bindtap="showPageInput">
            <text class="input-icon">⌨️</text>
          </view>
        </view>

        <!-- 下一页 -->
        <view class="page-btn {{currentPage >= totalPages ? 'disabled' : ''}}"
              bindtap="{{currentPage >= totalPages ? '' : 'onNextPage'}}">
          <text class="page-btn-text">下一页</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 页码输入组件 -->
  <page-input
    show="{{showPageInputModal}}"
    total-pages="{{totalPages}}"
    current-page="{{currentPage}}"
    bind:confirm="onPageInputConfirm"
    bind:cancel="onPageInputCancel"
  />

  <!-- 说明区域 -->
  <view class="info-section">
    <view class="info-content">
      <view class="info-title">🎮 多关卡说明</view>
      <view class="info-list">
        <view class="info-item">• 需要按顺序完成关卡</view>
        <view class="info-item" wx:if="{{mode !== 'elimination'}}">• 正确率达到80%以上才能解锁下一关</view>
        <view class="info-item" wx:if="{{mode === 'elimination'}}">• 完成消消乐游戏即可解锁下一关</view>
        <view class="info-item">• 每个关卡都有独立的排行榜</view>
        <view class="info-item">• 已完成的关卡可以重复挑战刷新成绩</view>
      </view>
    </view>
  </view>
</view> 