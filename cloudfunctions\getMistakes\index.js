const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const mistakesCollection = db.collection('mistakes')
const wordsCollection = db.collection('words')

exports.main = async (event, context) => {
  const { userId, type, skip = 0, limit = 20 } = event
  let query = mistakesCollection.where({ userId })
  if (type) query = query.where({ type })
  try {
    const res = await query.skip(skip).limit(limit).get()
    return { code: 200, data: res.data }
  } catch (e) {
    return { code: 500, message: '获取失败', error: e }
  }
} 