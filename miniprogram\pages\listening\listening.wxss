/* 听口练习页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  padding-bottom: 100rpx;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}

.exercises-container {
  margin-bottom: 40rpx;
}

.exercises-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.exercise-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.exercise-card:active {
  transform: scale(0.98);
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.exercise-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.exercise-info {
  flex: 1;
}

.exercise-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.exercise-subtitle {
  font-size: 24rpx;
  color: #666;
}

.card-content {
  margin-bottom: 20rpx;
}

.exercise-description {
  font-size: 28rpx;
  color: #555;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.exercise-meta {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.meta-label {
  color: #888;
  margin-right: 5rpx;
}

.meta-value {
  color: #333;
  font-weight: 500;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
}

.start-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.quick-actions {
  margin-bottom: 40rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 20rpx;
  border-radius: 15rpx;
  border: none;
  font-size: 26rpx;
  font-weight: 500;
  gap: 10rpx;
}

.action-btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.btn-icon {
  font-size: 40rpx;
}

.btn-text {
  font-size: 24rpx;
}

.tips-section {
  margin-bottom: 40rpx;
}

.tips-container {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 25rpx;
  backdrop-filter: blur(10rpx);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
  color: white;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-dot {
  color: #ffd700;
  font-weight: bold;
  margin-right: 10rpx;
  margin-top: 2rpx;
}

.tip-text {
  font-size: 26rpx;
  line-height: 1.4;
  flex: 1;
}

.exam-info {
  margin-bottom: 20rpx;
}

.info-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 25rpx;
  backdrop-filter: blur(10rpx);
}

.info-text {
  font-size: 26rpx;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
}

/* 音标专练 */
.phonetic-section {
  margin-bottom: 40rpx;
}

.phonetic-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.phonetic-card:active {
  transform: scale(0.98);
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.15);
}

.phonetic-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
  color: white;
  display: flex;
  align-items: center;
}

.phonetic-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.phonetic-info {
  flex: 1;
}

.phonetic-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.phonetic-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.arrow-icon {
  font-size: 32rpx;
  opacity: 0.8;
}

.phonetic-content {
  padding: 30rpx;
}

.phonetic-description {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.phonetic-features {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.feature-tag {
  background: rgba(255, 107, 107, 0.1);
  color: #FF6B6B;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  border: 2rpx solid rgba(255, 107, 107, 0.3);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .types-grid {
    grid-template-columns: 1fr;
  }
  
  .header {
    padding: 60rpx 40rpx 40rpx;
  }
  
  .header-title {
    font-size: 48rpx;
  }
} 