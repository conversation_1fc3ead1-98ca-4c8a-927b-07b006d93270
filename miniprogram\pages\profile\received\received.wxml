<!--pages/profile/received/received.wxml-->
<view class="received-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">📨 收到的分享</text>
      <text class="page-subtitle">管理您参与的分享测试</text>
    </view>
    <view class="header-stats" wx:if="{{statistics.totalShares > 0}}">
      <view class="stat-item">
        <text class="stat-number">{{statistics.totalShares}}</text>
        <text class="stat-label">个分享</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.totalTests}}</text>
        <text class="stat-label">次测试</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.averageScore}}</text>
        <text class="stat-label">平均分</text>
      </view>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-container">
      <input 
        class="share-id-input" 
        placeholder="请输入测试ID" 
        value="{{shareId}}"
        bindinput="onShareIdInput"
        maxlength="50"
      />
      <button 
        class="clear-btn" 
        wx:if="{{shareId}}"
        bindtap="clearInput"
      >
        ×
      </button>
    </view>
    
    <button 
      class="join-btn" 
      bindtap="joinShareTest"
      loading="{{loading}}"
      disabled="{{!shareId || loading}}"
    >
      {{loading ? '检查中...' : '参与测试'}}
    </button>
  </view>

  <!-- 分享测试按模式分组列表 -->
  <view class="shares-section" wx:if="{{modeGroups.length > 0}}">
    <!-- 工具栏 -->
    <view class="toolbar">
      <view class="toolbar-left">
        <button class="toolbar-btn" bindtap="toggleSelectAll">
          {{allSelected ? '取消全选' : '全选'}}
        </button>
        <text class="shares-count">共{{statistics.totalShares}}个分享</text>
      </view>
      <view class="toolbar-right">
        <button class="toolbar-btn danger" bindtap="batchDeleteShares">
          删除选中
        </button>
      </view>
    </view>

    <!-- 模式分组列表 -->
    <view class="mode-groups">
      <view
        class="mode-group"
        wx:for="{{modeGroups}}"
        wx:key="mode"
        wx:for-item="modeGroup"
      >
        <!-- 模式标题栏 -->
        <view
          class="mode-header"
          bindtap="toggleModeExpand"
          data-mode="{{modeGroup.mode}}"
        >
          <view class="mode-info">
            <text class="mode-icon">{{modeGroup.icon}}</text>
            <text class="mode-title">{{modeGroup.name}}</text>
            <text class="mode-count">({{modeGroup.stats.totalShares}})</text>
          </view>
          <view class="mode-stats" wx:if="{{modeGroup.expanded}}">
            <text class="stat">{{modeGroup.stats.totalTests}}次测试</text>
            <text class="stat">平均{{modeGroup.stats.averageScore}}分</text>
          </view>
          <view class="mode-actions">
            <text class="expand-icon {{modeGroup.expanded ? 'expanded' : ''}}">▼</text>
          </view>
        </view>

        <!-- 分享列表 -->
        <view
          class="shares-list {{modeGroup.expanded ? 'expanded' : ''}}"
          wx:if="{{modeGroup.expanded && modeGroup.shares.length > 0}}"
        >
          <view
            class="share-item {{item.isExpired ? 'expired' : ''}}"
            wx:for="{{modeGroup.shares}}"
            wx:key="shareId"
          >
        <!-- 选择框和内容的容器 -->
        <view class="share-main">
          <!-- 选择框 -->
          <view class="share-checkbox" bindtap="toggleSelectShare" data-mode="{{modeGroup.mode}}" data-index="{{index}}">
            <view class="checkbox {{item.isSelected ? 'checked' : ''}}">
              <text wx:if="{{item.isSelected}}" class="checkbox-icon">✓</text>
            </view>
          </view>

          <!-- 分享信息 -->
          <view class="share-content" bindtap="viewShareDetail" data-mode="{{modeGroup.mode}}" data-index="{{index}}">
            <!-- 标题和状态 -->
            <view class="share-header">
              <text class="share-title">{{item.shareTitle}}</text>
              <view class="share-badges">
                <text class="test-mode-badge">{{getTestModeText(item.testMode)}}</text>
                <text class="share-status {{item.isExpired ? 'expired' : 'active'}}">
                  {{item.isExpired ? '已过期' : '进行中'}}
                </text>
              </view>
            </view>

            <!-- 分享ID和多关卡标识 -->
            <view class="share-meta">
              <text class="share-id">ID: {{item.shareId}}</text>
              <text wx:if="{{item.isMultiLevel}}" class="multi-level-tag">多关卡</text>
              <!-- 进度显示 -->
              <text wx:if="{{item.isMultiLevel}}" class="progress-indicator">
                {{(item.myInfo && item.myInfo.progress && item.myInfo.progress.completedLevels ? item.myInfo.progress.completedLevels.length : 0)}}/{{item.totalLevels || 1}}
              </text>
            </view>

            <!-- 多关卡进度 -->
            <view wx:if="{{item.isMultiLevel}}" class="level-progress">
              <text class="progress-text">{{item.levelProgressText}}</text>
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{(item.myInfo.progress && item.myInfo.progress.completedLevels ? item.myInfo.progress.completedLevels.length : 0) / (item.totalLevels || 1) * 100}}%;"></view>
              </view>
            </view>

            <!-- 测试数据 -->
            <view class="test-stats">
              <view class="stat-row">
                <view class="stat-item">
                  <text class="stat-value">{{item.myInfo.testCount || 0}}</text>
                  <text class="stat-label">次测试</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">{{item.myInfo.bestScore || 0}}</text>
                  <text class="stat-label">最高分</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">{{item.myInfo.latestScore || 0}}</text>
                  <text class="stat-label">最新分</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">{{item.myInfo.latestAccuracy || 0}}%</text>
                  <text class="stat-label">正确率</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="share-actions">
          <!-- 主要操作按钮 -->
          <button
            wx:if="{{item.isMultiLevel && !item.isExpired}}"
            class="action-btn primary"
            bindtap="continueNextLevel"
            data-mode="{{modeGroup.mode}}"
            data-index="{{index}}"
          >
            继续下一关
          </button>
          <button
            wx:else
            class="action-btn {{item.isExpired ? 'disabled' : 'primary'}}"
            bindtap="retakeTest"
            data-mode="{{modeGroup.mode}}"
            data-index="{{index}}"
            disabled="{{item.isExpired}}"
          >
            {{item.isExpired ? '已过期' : (item.isMultiLevel ? '重新挑战' : '继续测试')}}
          </button>

          <!-- 查看记录按钮 -->
          <button
            class="action-btn secondary"
            bindtap="viewMyResults"
            data-mode="{{modeGroup.mode}}"
            data-index="{{index}}"
          >
            查看记录
          </button>
        </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-section" wx:if="{{modeGroups.length === 0 && !loading}}">
    <view class="empty-icon">📝</view>
    <text class="empty-text">还没有参与过分享测试</text>
    <text class="empty-desc">输入朋友分享的测试ID开始第一次测试吧</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 历史记录 -->
  <view class="history-section" wx:if="{{showHistory && recentShares.length > 0}}">
    <view class="history-header">
      <text class="history-title">📋 最近参与的测试</text>
    </view>
    
    <view class="history-list">
      <view 
        class="history-item" 
        wx:for="{{recentShares}}" 
        wx:key="index"
        bindtap="rejoinTest"
        data-index="{{index}}"
      >
        <view class="history-info">
          <view class="history-main">
            <text class="test-type">{{getTestModeText(item.testMode)}}</text>
            <text class="test-score">{{item.score}}分 ({{item.accuracy}}%)</text>
          </view>
          <view class="history-meta">
            <text class="share-id">ID: {{item.shareId}}</text>
            <text class="test-time">{{formatTime(item.timestamp)}}</text>
          </view>
        </view>
        <view class="rejoin-icon">→</view>
      </view>
    </view>
  </view>
</view>

<!-- 详情弹窗 -->
<view wx:if="{{showDetailModal}}" class="modal-overlay" bindtap="closeDetailModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">测试详情</text>
      <text class="modal-close" bindtap="closeDetailModal">×</text>
    </view>
    
    <view class="modal-body">
      <view class="detail-section">
        <view class="detail-row">
          <text class="detail-label">测试名称:</text>
          <text class="detail-value">{{currentDetail.shareTitle}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">测试类型:</text>
          <text class="detail-value">{{getTestModeText(currentDetail.testType)}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">测试ID:</text>
          <text class="detail-value">{{currentDetail.shareId}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">首次参与:</text>
          <text class="detail-value">{{currentDetail.firstVisitTimeText}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">最近测试:</text>
          <text class="detail-value">{{currentDetail.lastTestTimeText}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">测试次数:</text>
          <text class="detail-value">{{currentDetail.myInfo.testCount || 0}}次</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">最高分:</text>
          <text class="detail-value">{{currentDetail.myInfo.bestScore || 0}}分</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">平均分:</text>
          <text class="detail-value">{{currentDetail.averageScore}}分</text>
        </view>
        
        <!-- 多关卡任务详情 -->
        <view wx:if="{{currentDetail.isMultiLevel}}" class="level-detail">
          <view class="detail-row">
            <text class="detail-label">总关卡数:</text>
            <text class="detail-value">{{currentDetail.totalLevels}}关</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">当前关卡:</text>
            <text class="detail-value">第{{currentDetail.myInfo.progress.currentLevel || 1}}关</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">完成关卡:</text>
            <text class="detail-value">{{currentDetail.myInfo.progress.completedLevels.length || 0}}关</text>
          </view>
          
          <!-- 关卡分数详情 -->
          <view class="level-scores">
            <view class="section-title">关卡得分</view>
            <view class="level-score-list">
              <view 
                wx:for="{{currentDetail.myInfo.progress.scores}}" 
                wx:key="*this" 
                class="level-score-item"
              >
                <text class="level-name">第{{index}}关:</text>
                <text class="level-score">{{item}}分</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>