/* 测试页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

/* 测试内容区域 */
.test-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 顶部信息栏 */
.test-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 25rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
}

.progress-info {
  flex: 1;
}

.progress-text {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.timer {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border-radius: 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.time-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 题目区域 */
.question-area {
  margin-bottom: 40rpx;
}

.mode-indicator {
  text-align: center;
  margin-bottom: 20rpx;
}

.mode-text {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 10rpx 30rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.question-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  min-height: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.question-content {
  margin-bottom: 20rpx;
}

.question-text {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  word-break: break-all;
}

.phonetic {
  margin-top: 10rpx;
}

.phonetic-text {
  font-size: 24rpx;
  color: #666;
  font-style: italic;
}

/* 选项区域 */
.options-area {
  flex: 1;
  margin-bottom: 30rpx;
}

.options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.option-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  position: relative;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  overflow: hidden;
}

.option-item:active {
  transform: scale(0.95);
}

.option-item.correct {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border-color: #4CAF50;
  box-shadow: 0 8rpx 25rpx rgba(76, 175, 80, 0.4);
  animation: correctPulse 0.6s ease-out;
}

.option-item.wrong {
  background: linear-gradient(135deg, #f44336, #e53935);
  color: white;
  border-color: #f44336;
  box-shadow: 0 8rpx 25rpx rgba(244, 67, 54, 0.4);
  animation: wrongShake 0.6s ease-out;
}

/* 正确答案动画 */
@keyframes correctPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15rpx 35rpx rgba(76, 175, 80, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8rpx 25rpx rgba(76, 175, 80, 0.4);
  }
}

/* 错误答案动画 */
@keyframes wrongShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-8rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(8rpx);
  }
}

.option-content {
  display: flex;
  align-items: flex-start;
  width: 100%;
  z-index: 2;
  position: relative;
}

.option-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-right: 16rpx;
  flex-shrink: 0;
  line-height: 1.2;
}

.option-item.correct .option-label,
.option-item.wrong .option-label {
  color: white;
}

.option-text {
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.2;
  word-break: break-all;
  flex: 1;
}

.option-indicator {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #4CAF50;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  animation: indicatorPop 0.4s ease-out;
  z-index: 3;
}

.option-indicator.wrong {
  background: rgba(255, 255, 255, 0.9);
  color: #f44336;
}

/* 指示器弹出动画 */
@keyframes indicatorPop {
  0% {
    transform: scale(0) rotate(180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(90deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* 添加选项的光效背景 */
.option-item.correct::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: correctSweep 0.8s ease-out;
  z-index: 1;
}

.option-item.wrong::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  animation: wrongFlash 0.4s ease-out;
  z-index: 1;
}

/* 正确答案光效扫过动画 */
@keyframes correctSweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 错误答案闪烁动画 */
@keyframes wrongFlash {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

/* 统计信息栏 */
.stats-bar {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 20rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 10rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.stat-value.correct {
  color: #4CAF50;
}

.stat-value.wrong {
  color: #f44336;
}

/* 结果页面 */
.result-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.result-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx;
  padding: 50rpx 40rpx;
  text-align: center;
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 600rpx;
}

.result-header {
  margin-bottom: 40rpx;
}

.result-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.result-stats {
  margin-bottom: 40rpx;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-label {
  font-size: 28rpx;
  color: #666;
}

.result-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.result-value.correct {
  color: #4CAF50;
}

.result-value.wrong {
  color: #f44336;
}

.result-value.score {
  color: #667eea;
}

.result-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 竞赛模式下的四个按钮网格布局 */
.competition-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  width: 100%;
}

/* 非竞赛模式下的正常按钮布局 */
.normal-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn::after {
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.action-btn.secondary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.action-btn.warning {
  background: linear-gradient(135deg, #ff9500, #ff6b35);
  color: white;
}

.action-btn:not(.primary):not(.secondary):not(.warning) {
  background: #f8f9fa;
  color: #666;
}

.action-btn.disabled {
  background: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.disabled:active {
  transform: none;
}

/* 等待页面 */
.waiting-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.waiting-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.2);
}

.waiting-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.waiting-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.test-header, .question-area, .options-area, .stats-bar {
  animation: fadeInUp 0.6s ease-out;
}

.test-header {
  animation-delay: 0.1s;
}

.question-area {
  animation-delay: 0.2s;
}

.options-area {
  animation-delay: 0.3s;
}

.stats-bar {
  animation-delay: 0.4s;
}

/* 练习模式样式 */
.practice-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80rpx);
}

.practice-header {
  margin-bottom: 40rpx;
}

.word-card-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.word-card-large {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  color: #333;
  max-width: 600rpx;
  width: 100%;
}

.word-section {
  text-align: center;
  margin-bottom: 40rpx;
  padding-bottom: 40rpx;
  border-bottom: 2rpx solid #eee;
}

.word-text-large {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.phonetic-large {
  display: block;
  font-size: 28rpx;
  color: #666;
  font-style: italic;
}

.meaning-section, .example-section {
  margin-bottom: 32rpx;
}

.meaning-title, .example-title {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
  font-weight: 600;
}

.meaning-text-large {
  display: block;
  font-size: 32rpx;
  color: #333;
  line-height: 1.5;
}

.example-text-large {
  display: block;
  font-size: 26rpx;
  color: #555;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.practice-nav {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 0;
}

.nav-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.nav-btn.primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.nav-btn.disabled {
  opacity: 0.5;
}

.nav-btn.mastered {
  background: linear-gradient(135deg, #43E97B 0%, #38F9D7 100%);
}

/* 点击显示样式 */
.reveal-sections {
  margin-top: 20rpx;
}

.reveal-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.reveal-btn:active {
  transform: scale(0.98);
}

.reveal-btn.revealed {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
}

.reveal-title {
  font-size: 26rpx;
  font-weight: 600;
}

.reveal-icon {
  font-size: 24rpx;
  opacity: 0.8;
}

.english-reveal {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* AI助记区域样式 */
.ai-memory-section {
  margin-top: 24rpx;
}

.ai-memory-btn {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: 2rpx solid transparent;
  color: white;
}

.ai-memory-btn .reveal-title,
.ai-memory-btn .reveal-icon {
  color: white;
}

.ai-memory-btn.revealed {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
}

.ai-memory-content {
  margin-top: 16rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #6366f1;
}

.ai-memory-loading {
  text-align: center;
  padding: 32rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #6366f1;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
  from { opacity: 0.6; }
  to { opacity: 1; }
}

.ai-memory-result {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.ai-memory-text {
  display: block;
  font-size: 28rpx;
  line-height: 1.8;
  color: #374151;
  white-space: pre-wrap;
  word-break: break-word;
  margin-bottom: 20rpx;
}

.ai-memory-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

.ai-action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: white;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.ai-action-btn:active {
  background: #f3f4f6;
  transform: scale(0.95);
}

.action-icon {
  font-size: 24rpx;
}

.action-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.ai-memory-error {
  text-align: center;
  padding: 24rpx 0;
}

.error-text {
  display: block;
  font-size: 26rpx;
  color: #ef4444;
  margin-bottom: 16rpx;
}

.retry-btn {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background: #fee2e2;
  border: 2rpx solid #fecaca;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.retry-btn:active {
  background: #fca5a5;
  transform: scale(0.95);
}

.retry-text {
  font-size: 26rpx;
  color: #dc2626;
  font-weight: 500;
}

.ai-memory-empty {
  text-align: center;
  padding: 24rpx 0;
}

.empty-text {
  display: block;
  font-size: 26rpx;
  color: #9ca3af;
  margin-bottom: 16rpx;
}

.generate-btn-small {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.generate-btn-small:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(99, 102, 241, 0.4);
}

.generate-text {
  font-size: 28rpx;
  color: white;
  font-weight: 600;
}

/* ================ 发音按钮样式 ================ */

/* 练习模式单词头部 */
.word-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 15rpx;
}

.word-header .word-text-large {
  margin-bottom: 0;
}

/* 测试模式问题头部 */
.question-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

/* 汉译英模式英文内容 */
.english-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  margin-bottom: 10rpx;
}

/* 主要播放按钮（练习模式） */
.play-btn {
  min-width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.play-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.4);
}

.play-btn.playing {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  animation: playingPulse 1.5s ease-in-out infinite alternate;
}

@keyframes playingPulse {
  from { 
    box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
  }
  to { 
    box-shadow: 0 6rpx 20rpx rgba(255, 152, 0, 0.5);
  }
}

.play-btn::after {
  border: none;
}

.play-icon {
  font-size: 36rpx;
  line-height: 1;
}

/* 小型播放按钮（汉译英模式） */
.play-btn-small {
  min-width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3rpx 8rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.play-btn-small:active {
  transform: scale(0.95);
}

.play-btn-small.playing {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  animation: playingPulse 1.5s ease-in-out infinite alternate;
}

.play-btn-small::after {
  border: none;
}

.play-icon-small {
  font-size: 28rpx;
  line-height: 1;
}

/* 测试模式播放按钮 */
.play-btn-test {
  min-width: 70rpx;
  height: 70rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3rpx 10rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  margin-left: 15rpx;
}

.play-btn-test:active {
  transform: scale(0.95);
}

.play-btn-test.playing {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  animation: playingPulse 1.5s ease-in-out infinite alternate;
}

.play-btn-test::after {
  border: none;
}

.play-icon-test {
  font-size: 32rpx;
  line-height: 1;
}

/* 播放按钮禁用状态 */
.play-btn:disabled,
.play-btn-small:disabled,
.play-btn-test:disabled {
  opacity: 0.5;
  transform: none !important;
  animation: none !important;
  background: #ccc;
  box-shadow: none;
}

/* 调试区域样式 */
.debug-section {
  margin-top: 20rpx;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
  text-align: center;
}

.debug-btn {
  background-color: #ff6b6b;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.debug-btn:active {
  transform: scale(0.95);
}

/* ================ 分享弹窗样式 ================ */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.share-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: calc(100% - 80rpx);
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.share-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-info {
  margin-bottom: 40rpx;
}

.share-desc {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.share-note {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.share-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.wechat-share-btn, .copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 32rpx;
  border: none;
  gap: 15rpx;
}

.wechat-share-btn {
  background: linear-gradient(135deg, #07c160, #00ae47);
  color: white;
}

.copy-btn {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e0e0e0;
}

.share-btn-icon, .copy-btn-icon {
  font-size: 36rpx;
}

.share-btn-text, .copy-btn-text {
  font-size: 32rpx;
  font-weight: bold;
}

/* 填空题样式 */
.input-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40rpx 0;
}

.input-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin: 0 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-label {
  font-size: 32rpx;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
  font-weight: 500;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.word-input {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 15rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  background: #fff;
  transition: all 0.3s ease;
}

.word-input.has-content {
  border-color: #667eea;
  background: #f8f9ff;
}

.word-input.correct {
  border-color: #4caf50;
  background: #f1f8e9;
  color: #2e7d32;
}

.word-input.wrong {
  border-color: #f44336;
  background: #ffebee;
  color: #c62828;
}

.submit-btn {
  width: 120rpx;
  height: 80rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.submit-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.submit-btn.disabled {
  background: #e0e0e0;
  color: #999;
}

.answer-display {
  margin-top: 20rpx;
}

.answer-result {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  padding: 20rpx;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
}

.answer-result.correct {
  background: #e8f5e8;
  color: #2e7d32;
}

.answer-result.wrong {
  background: #ffebee;
  color: #c62828;
}

.result-icon {
  font-size: 36rpx;
  font-weight: bold;
}

.result-text {
  font-size: 30rpx;
  font-weight: 500;
}

.correct-answer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  padding: 15rpx 20rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.answer-label {
  font-size: 26rpx;
  color: #666;
}

.answer-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.play-btn-small {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.play-btn-small.playing {
  background: linear-gradient(135deg, #764ba2, #667eea);
  transform: scale(1.1);
}

.play-icon-small {
  font-size: 24rpx;
}

.phonetic-display {
  text-align: center;
  padding: 10rpx;
}

.phonetic-text {
  font-size: 26rpx;
  color: #666;
  font-style: italic;
}

/* ========================= 详细测试结果页面样式 ========================= */

/* 重新定义结果页面为全屏模态 */
.result-content {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.6) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000 !important;
  padding: 20rpx !important;
}

.result-card {
  background: white !important;
  border-radius: 20rpx !important;
  max-height: 90vh !important;
  width: 100% !important;
  max-width: 700rpx !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.3) !important;
  padding: 0 !important;
}

.result-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 30rpx !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  margin-bottom: 0 !important;
}

.result-title {
  font-size: 36rpx !important;
  font-weight: bold !important;
  color: white !important;
}

.result-close {
  font-size: 40rpx;
  cursor: pointer;
  padding: 10rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stats-overview {
  padding: 30rpx;
  background: #f8f9fa;
}

.stats-card {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 15rpx;
  padding: 25rpx 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 48rpx !important;
  font-weight: bold !important;
  margin-bottom: 8rpx !important;
  color: #333 !important;
}

.stat-value.correct {
  color: #4CAF50 !important;
}

.stat-value.wrong {
  color: #f44336 !important;
}

.stat-value.accuracy {
  color: #2196F3 !important;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.test-time {
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

.time-label {
  margin-right: 10rpx;
}

.time-value {
  font-weight: bold;
  color: #333;
}

.answer-results-section {
  padding: 0 30rpx;
  margin-bottom: 10rpx;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  flex-shrink: 0;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.select-actions {
  display: flex;
  gap: 15rpx;
}

.select-btn {
  font-size: 22rpx;
  color: #2196F3;
  padding: 6rpx 12rpx;
  border: 1rpx solid #2196F3;
  border-radius: 12rpx;
  cursor: pointer;
}

.select-btn.primary {
  background: #2196F3;
  color: white;
}

.results-list {
  flex: 1;
  overflow-y: auto;
  min-height: 400rpx;
  max-height: 800rpx;
  padding-bottom: 10rpx;
}

.result-item {
  background: #f8f9fa !important;
  border-radius: 8rpx !important;
  padding: 10rpx 15rpx !important;
  margin-bottom: 6rpx !important;
  border: 2rpx solid transparent !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  gap: 10rpx !important;
  justify-content: flex-start !important;
  min-height: 45rpx !important;
}

.result-item.selected {
  border-color: #4CAF50 !important;
  background: #e8f5e8 !important;
}

.result-item.correct {
  border-left: 4rpx solid #4CAF50 !important;
}

.result-item.wrong {
  border-left: 4rpx solid #f44336 !important;
}





/* 错词结果一行显示 - 统一样式 */
.word-content-inline {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 3rpx;
  white-space: nowrap;
  overflow: hidden;
}

/* 单词 - 统一样式 */
.word-text-inline {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
  flex-shrink: 0;
  max-width: 110rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 18rpx;
}

/* 您的答案、正确答案标签 - 统一样式 */
.answer-label-inline {
  font-size: 13rpx;
  color: #666;
  flex-shrink: 0;
  white-space: nowrap;
  margin-right: 2rpx;
}

/* 答案内容 - 统一样式 */
.answer-text-inline {
  font-size: 13rpx;
  font-weight: 500;
  flex-shrink: 0;
  max-width: 140rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 6rpx;
}

/* 错误答案红色 */
.answer-text-inline.wrong-inline {
  color: #f44336;
}

/* 正确答案绿色 */
.answer-text-inline.correct-inline {
  color: #4CAF50;
}

/* 分隔符 - 统一样式 */
.answer-separator-inline {
  font-size: 13rpx;
  color: #ccc;
  margin: 0 6rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

.select-checkbox {
  position: static;
  flex-shrink: 0;
}

.checkbox {
  font-size: 24rpx;
}

.item-number {
  background: #e9ecef;
  color: #666;
  font-size: 16rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.result-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
}

.status-icon {
  font-size: 32rpx;
}

.perfect-score {
  text-align: center;
  padding: 40rpx 30rpx;
  background: #f8f9fa;
}

.perfect-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.perfect-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 15rpx;
}

.perfect-desc {
  font-size: 28rpx;
  color: #666;
}

/* 错词重测选项区域 */
.retry-options-section {
  padding: 15rpx 30rpx;
  background: #f8f9fa;
  margin-top: 5rpx;
}

.retry-title {
  margin-bottom: 15rpx;
  text-align: center;
}

.retry-title-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 测试模式选项 - 一行显示 */
.retry-modes {
  display: flex;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

.mode-btn {
  flex: 1;
  background: white !important;
  border: 2rpx solid #e9ecef !important;
  border-radius: 12rpx !important;
  padding: 12rpx 6rpx !important;
  font-size: 22rpx !important;
  font-weight: 500 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4rpx !important;
  min-height: 65rpx !important;
  transition: all 0.3s ease !important;
  color: #333 !important;
}

.mode-icon {
  font-size: 22rpx;
  line-height: 1;
  color: inherit;
}

.mode-text {
  font-size: 18rpx;
  line-height: 1.1;
  text-align: center;
  color: inherit;
}

/* 分享选项 - 单独一行 */
.share-option {
  border-top: 1rpx solid #e9ecef;
  padding-top: 15rpx;
  display: flex;
  justify-content: center;
}

.share-btn {
  background: white !important;
  border: 2rpx solid #9c27b0 !important;
  border-radius: 12rpx !important;
  padding: 12rpx 25rpx !important;
  font-size: 24rpx !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 6rpx !important;
  color: #9c27b0 !important;
  min-width: 180rpx !important;
  height: 50rpx !important;
}

.share-btn .share-icon,
.share-btn .share-text {
  color: #9c27b0 !important;
}

.share-icon {
  font-size: 20rpx;
  line-height: 1;
  color: inherit;
}

.share-text {
  font-size: 20rpx;
  line-height: 1;
  color: inherit;
}

/* 无错词提示样式 */
.no-wrong-words {
  text-align: center;
  padding: 60rpx 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin: 20rpx 0;
}

.no-wrong-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.no-wrong-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 10rpx;
}

.no-wrong-desc {
  font-size: 26rpx;
  color: #666;
}

.mode-btn.retry-same {
  border-color: #2196F3 !important;
  color: #2196F3 !important;
}

.mode-btn.retry-same .mode-icon,
.mode-btn.retry-same .mode-text {
  color: #2196F3 !important;
}

.mode-btn.retry-opposite {
  border-color: #4CAF50 !important;
  color: #4CAF50 !important;
}

.mode-btn.retry-opposite .mode-icon,
.mode-btn.retry-opposite .mode-text {
  color: #4CAF50 !important;
}

.mode-btn.retry-dictation {
  border-color: #667eea !important;
  color: #667eea !important;
}

.mode-btn.retry-dictation .mode-icon,
.mode-btn.retry-dictation .mode-text {
  color: #667eea !important;
}

.mode-btn.retry-game {
  border-color: #ff9800 !important;
  color: #ff9800 !important;
}

.mode-btn.retry-game .mode-icon,
.mode-btn.retry-game .mode-text {
  color: #ff9800 !important;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 24rpx;
}

.group-test-section {
  background: #e3f2fd;
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
}

.group-info {
  margin-bottom: 20rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 10rpx;
}

.group-progress {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}

.group-actions {
  display: flex;
  gap: 15rpx;
}

.group-btn {
  flex: 1;
  padding: 15rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  border: none;
}

.group-btn.primary {
  background: #2196F3;
  color: white;
}

.group-btn.warning {
  background: #ff9800;
  color: white;
}

.bottom-actions {
  display: flex;
  gap: 12rpx;
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #e9ecef;
}

.bottom-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  border: none;
}

.bottom-btn.share-test {
  background: #9c27b0;
  color: white;
}

.bottom-btn.secondary {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.bottom-btn:not(.secondary):not(.share-test) {
  background: #667eea;
  color: white;
}

/* 🔧 短语检测专用样式 - 增加短语显示宽度 */
.word-content-flex {
  flex: 1;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
}

/* 普通单词样式 */
.word-text-phrase {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
  flex-shrink: 0;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 18rpx;
}

/* 短语模式下的单词文本 */
.result-item.phrase-mode .word-text-phrase {
  max-width: 300rpx !important; /* 大幅增加短语显示宽度 */
  font-size: 20rpx !important;
  background: #fff3cd !important; /* 临时调试：黄色背景 */
  padding: 2rpx 4rpx !important;
  border-radius: 4rpx !important;
}

/* 答案标签 */
.answer-label {
  font-size: 13rpx;
  color: #666;
  flex-shrink: 0;
  margin-right: 2rpx;
}

/* 用户答案 */
.user-answer-phrase {
  font-size: 13rpx;
  color: #f44336;
  flex-shrink: 0;
  max-width: 140rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 6rpx;
}

/* 短语模式下的用户答案 */
.result-item.phrase-mode .user-answer-phrase {
  max-width: 200rpx !important;
}

/* 正确答案 */
.correct-answer-phrase {
  font-size: 13rpx;
  color: #4CAF50;
  flex-shrink: 0;
  max-width: 160rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 短语模式下的正确答案 */
.result-item.phrase-mode .correct-answer-phrase {
  max-width: 250rpx !important;
}

/* 分隔符 */
.separator {
  font-size: 13rpx;
  color: #ccc;
  flex-shrink: 0;
  margin-right: 6rpx;
}

/* 短语模式整体样式 */
.result-item.phrase-mode {
  background: #f8f9fa !important; /* 临时调试：浅灰背景 */
  border-left: 4rpx solid #ffc107 !important; /* 临时调试：黄色左边框 */
}