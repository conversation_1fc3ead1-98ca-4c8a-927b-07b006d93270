<view class="container">
  <!-- 进度条 -->
  <view class="progress-bar">
    <view class="progress-inner" style="width: {{progress}}%"></view>
    <text class="progress-text">{{currentIndex + 1}}/{{total}}</text>
  </view>

  <!-- 练习卡片 -->
  <view class="practice-card">
    <!-- 音频播放区域 -->
    <view class="audio-section">
      <view class="audio-header">
        <text class="audio-title">听音练习</text>
        <text class="audio-subtitle">请仔细听音频，选择正确的单词</text>
      </view>
      
      <view class="audio-player">
        <view class="play-btn {{isPlaying ? 'playing' : ''}}" bindtap="togglePlay">
          <image class="play-icon" src="/images/{{isPlaying ? 'pause' : 'play'}}.png" />
        </view>
        <slider 
          class="audio-slider" 
          value="{{audioProgress}}" 
          bindchange="onSliderChange"
          block-size="12"
          activeColor="#4CAF50"
          backgroundColor="#ddd"
        />
        <text class="time-text">{{currentTime}}/{{duration}}</text>
      </view>
    </view>

    <!-- 选项区域 -->
    <view class="options-section">
      <view class="options-list">
        <view class="option-item {{selectedOption === index ? 'selected' : ''}} {{showResult && index === correctIndex ? 'correct' : ''}} {{showResult && selectedOption === index && index !== correctIndex ? 'wrong' : ''}}"
              wx:for="{{options}}" 
              wx:key="index"
              bindtap="onOptionSelect"
              data-index="{{index}}">
          <text class="option-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons" wx:if="{{showResult}}">
      <button class="action-btn next-btn" bindtap="onNextWord">下一个</button>
    </view>
  </view>

  <!-- 完成提示 -->
  <view class="completion-modal" wx:if="{{showCompletion}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">练习完成</text>
      </view>
      <view class="modal-body">
        <view class="stat-item">
          <text class="stat-label">总用时</text>
          <text class="stat-value">{{formatTime(totalTime)}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">正确率</text>
          <text class="stat-value">{{correctRate}}%</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">错题数</text>
          <text class="stat-value">{{mistakes.length}}</text>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn" bindtap="onAddToMistakes" wx:if="{{mistakes.length > 0}}">加入错题本</button>
        <button class="modal-btn" bindtap="onRetryMistakes" wx:if="{{mistakes.length > 0}}">错题重练</button>
        <button class="modal-btn" bindtap="onBackToHome">返回首页</button>
      </view>
    </view>
  </view>
</view> 