/* 听后选择练习页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  margin-bottom: 30rpx;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 30rpx;
  color: white;
  font-weight: bold;
}

.title-info {
  flex: 1;
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 5rpx;
}

.progress {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.section-tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* 流程指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  padding: 0 40rpx;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.step.active .step-number {
  background: #ffd700;
  color: #333;
}

.step.completed .step-number {
  background: #4caf50;
  color: white;
}

.step-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.step.active .step-label {
  color: #ffd700;
  font-weight: bold;
}

.step-line {
  width: 80rpx;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 20rpx;
  margin-top: -30rpx;
}

/* 题目内容区域 */
.question-content {
  flex: 1;
}

/* 阅读阶段 */
.reading-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.timer-display {
  text-align: center;
  margin-bottom: 20rpx;
}

.timer-text {
  background: #ff6b6b;
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  font-weight: bold;
}

.question-card {
  background: white;
  border-radius: 15rpx;
  padding: 25rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
}

.question-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 10rpx;
}

.question-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.option-item.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.option-key {
  font-size: 26rpx;
  font-weight: bold;
  color: #667eea;
  margin-right: 15rpx;
  min-width: 40rpx;
}

.option-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

/* 音频播放阶段 */
.audio-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  text-align: center;
  margin-bottom: 20rpx;
}

.audio-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.audio-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.audio-icon.playing {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.audio-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.audio-status {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.play-count {
  font-size: 24rpx;
  color: #666;
}

.audio-tip {
  background: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 10rpx;
  padding: 15rpx;
  color: #856404;
  font-size: 24rpx;
  line-height: 1.4;
}

/* 答题阶段 */
.answering-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.submit-section {
  margin-top: 30rpx;
  text-align: center;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.submit-btn.disabled {
  background: #ccc;
  color: #999;
}

/* 结果显示阶段 */
.result-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.result-card {
  text-align: center;
}

.result-header {
  margin-bottom: 30rpx;
}

.result-icon {
  font-size: 80rpx;
  margin-bottom: 10rpx;
  display: block;
}

.result-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.answer-analysis {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
}

.your-answer, .correct-answer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.your-answer:last-child, .correct-answer:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: #666;
}

.answer {
  font-size: 26rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.answer.correct {
  background: #d4edda;
  color: #155724;
}

.answer.wrong {
  background: #f8d7da;
  color: #721c24;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 15rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #667eea;
  border: 2rpx solid #667eea;
}

/* 底部统计 */
.bottom-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 5rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
} 