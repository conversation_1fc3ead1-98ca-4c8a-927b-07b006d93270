const app = getApp();
const { LIBRARY_INFO, TEXTBOOK_UNITS } = require('../../../config/constants.js');

Page({
  data: {
    statusBarHeight: 0, // 状态栏高度
    // 页面参数
    returnTo: '', // 返回目标页面
    testType: '', // 测试类型 wordtest/phrasetest
    
    // 当前选择状态
    currentStage: 'category', // category-分类选择, library-具体词库, volume-册数选择, unit-单元选择
    selectedCategory: null,
    selectedLibrary: null,
    selectedVolume: null,
    selectedUnits: [],
    testOrder: 'random', // random-乱序, sequential-顺序
    
    // 词库分类数据
    categories: [
      {
        id: 'college',
        name: '大学词汇',
        icon: '🎓',
        type: 'direct'
      },
      {
        id: 'gaokao',
        name: '高考大纲词汇',
        icon: '📚',
        type: 'direct'
      },
      {
        id: 'textbook',
        name: '教材同步词汇',
        icon: '📖',
        type: 'hierarchical'
      },
      {
        id: 'phrase',
        name: '常用短语',
        icon: '💬',
        type: 'direct'
      },
      {
        id: 'special',
        name: '题型专项词汇',
        icon: '📝',
        type: 'direct'
      },
      {
        id: 'other',
        name: '其他词汇',
        icon: '📌',
        type: 'direct'
      },
      {
        id: 'zhongkao',
        name: '中考词汇',
        icon: '🎯',
        type: 'direct'
      },
      {
        id: 'custom',
        name: '自定义词库',
        icon: '✏️',
        type: 'custom'
      }
    ],
    
    // 具体词库数据
    libraries: {
      zhongkao: [
        { id: 'zhongkao_1600', name: '中考1600词', count: '加载中...' }
      ],
      college: [
        { id: 'college_cet4', name: '四级词汇', count: '加载中...' },
        { id: 'college_cet6', name: '六级词汇', count: '加载中...' }
      ],
      gaokao: [
        { id: 'gaokao_3500', name: '高考3500（顺序版）', count: '加载中...' },
        { id: 'gaokao_3500_luan', name: '高考3500（乱序版）', count: '加载中...' },
        { id: 'gaokao_weikeduo', name: '维克多高考词汇', count: '加载中...' }
      ],
      textbook: [
        { id: 'renjiao', name: '人教版', publisher: '人民教育出版社' },
        { id: 'beishi', name: '北师大版', publisher: '北京师范大学出版社' }
      ],
      phrase: [
        { id: 'phrase_gaopin', name: '高频短语', count: '加载中...' },
        { id: 'phrase_hunxiao', name: '易错易混淆短语', count: '加载中...' }
      ],
      special: [
        { id: 'special_wusan_gaopin_beijing', name: '五三高频词（北京卷）', count: '加载中...' },
        { id: 'special_yuedu_tongyong', name: '阅读高频词（通用）', count: '加载中...' },
        { id: 'special_yuedu_beijing', name: '阅读高频词（北京卷）', count: '加载中...' },
        { id: 'special_wanxing_gaopin_beijing', name: '完形高频词（北京卷）', count: '加载中...' },
        { id: 'special_wanxing_shucishengyi_beijing', name: '完型熟词生义（北京卷）', count: '加载中...' }
      ],
      other: [
        { id: 'other_buguize', name: '不规则动词', count: '加载中...' },
        { id: 'other_xingjinci', name: '高考形近词', count: '加载中...' },
        { id: 'other_shucishengyi', name: '熟词生义（通用）', count: '加载中...' }
      ]
    },
    
    // 教材册数数据
    volumes: {
      renjiao: [
        { id: 'bixiu1', name: '必修第一册', type: 'required', realId: 'renjiao_bixiu1' },
        { id: 'bixiu2', name: '必修第二册', type: 'required', realId: 'renjiao_bixiu2' },
        { id: 'bixiu3', name: '必修第三册', type: 'required', realId: 'renjiao_bixiu3' },
        { id: 'xuanxiu1', name: '选择性必修第一册', type: 'elective', realId: 'renjiao_xuanxiu1' },
        { id: 'xuanxiu2', name: '选择性必修第二册', type: 'elective', realId: 'renjiao_xuanxiu2' },
        { id: 'xuanxiu3', name: '选择性必修第三册', type: 'elective', realId: 'renjiao_xuanxiu3' },
        { id: 'xuanxiu4', name: '选择性必修第四册', type: 'elective', realId: 'renjiao_xuanxiu4' }
      ],
      beishi: [
        { id: 'bixiu1', name: '必修第一册', type: 'required', realId: 'beishi_bixiu1' },
        { id: 'bixiu2', name: '必修第二册', type: 'required', realId: 'beishi_bixiu2' },
        { id: 'bixiu3', name: '必修第三册', type: 'required', realId: 'beishi_bixiu3' },
        { id: 'xuanxiu1', name: '选择性必修第一册', type: 'elective', realId: 'beishi_xuanxiu1' },
        { id: 'xuanxiu2', name: '选择性必修第二册', type: 'elective', realId: 'beishi_xuanxiu2' },
        { id: 'xuanxiu3', name: '选择性必修第三册', type: 'elective', realId: 'beishi_xuanxiu3' },
        { id: 'xuanxiu4', name: '选择性必修第四册', type: 'elective', realId: 'beishi_xuanxiu4' }
      ]
    },
    
    // 单元数据（模拟数据，实际需要根据具体册数动态加载）
    units: [
      { id: 'welcome', name: 'Welcome Unit', wordCount: 67 },
      { id: 'unit1', name: 'Unit1', wordCount: 60 },
      { id: 'unit2', name: 'Unit2', wordCount: 86 },
      { id: 'unit3', name: 'Unit3', wordCount: 71 },
      { id: 'unit4', name: 'Unit4', wordCount: 96 },
      { id: 'unit5', name: 'Unit5', wordCount: 67 }
    ],
    
    // 自定义词库列表
    customWordbanks: []
  },

  onLoad(options) {
    const { returnTo, testType } = options;
    
    // 获取系统信息，设置状态栏高度
    let statusBarHeight = 0;
    try {
      const windowInfo = wx.getWindowInfo();
      statusBarHeight = windowInfo.statusBarHeight || 0;
    } catch (error) {
      console.warn('获取状态栏高度失败:', error);
    }
    
    this.setData({
      statusBarHeight: statusBarHeight,
      returnTo: returnTo || 'wordtest',
      testType: testType || 'word'
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: testType === 'phrase' ? '短语词库选择' : '单词词库选择'
    });

    // 加载自定义词库
    this.loadCustomWordbanks();

    // 加载词库真实数量
    this.loadLibrariesCount();
  },

  // 加载自定义词库
  async loadCustomWordbanks() {
    try {
      const userInfo = await app.getUserInfo();
      if (!userInfo || !userInfo.openid) {
        return;
      }

      const db = wx.cloud.database();
      const result = await db.collection('custom_wordbanks')
        .where({
          creatorOpenid: userInfo.openid
        })
        .orderBy('createTime', 'desc')
        .get();

      this.setData({
        customWordbanks: result.data
      });
    } catch (error) {
      console.error('加载自定义词库失败:', error);
    }
  },

  // 加载词库真实数量
  async loadLibrariesCount() {
    const libraries = this.data.libraries;

    // 需要加载数量的词库类型
    const typesToLoad = ['zhongkao', 'college', 'gaokao', 'phrase', 'special', 'other'];

    for (const type of typesToLoad) {
      const typeLibraries = libraries[type];
      if (typeLibraries && Array.isArray(typeLibraries)) {
        for (let i = 0; i < typeLibraries.length; i++) {
          const library = typeLibraries[i];
          try {
            const count = await this.getLibraryWordCount(library.id);
            // 更新对应词库的数量
            this.setData({
              [`libraries.${type}[${i}].count`]: `${count}词`
            });
          } catch (error) {
            console.error(`获取词库${library.id}数量失败:`, error);
            // 出错时显示默认值
            this.setData({
              [`libraries.${type}[${i}].count`]: '未知'
            });
          }
        }
      }
    }
  },

  // 获取单个词库的词汇数量
  async getLibraryWordCount(libraryId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getWords',
        data: {
          libraryId: libraryId,
          getTotalCount: true,
          limit: 1 // 只需要获取总数，不需要具体词汇
        }
      });

      if (result.result.code === 200) {
        return result.result.totalCount || 0;
      } else {
        console.error('获取词汇数量失败:', result.result.message);
        return 0;
      }
    } catch (error) {
      console.error('调用getWords云函数失败:', error);
      return 0;
    }
  },

  // 选择分类
  onCategorySelect(e) {
    const categoryId = e.currentTarget.dataset.categoryId;
    const category = this.data.categories.find(c => c.id === categoryId);
    
    console.log('选择分类:', category);

    if (category.type === 'custom') {
      // 自定义词库，直接显示词库列表
      this.setData({
        selectedCategory: category,
        selectedLibrary: null,
        selectedVolume: null,
        selectedUnits: [],
        currentStage: 'custom'
      });
    } else if (category.type === 'hierarchical') {
      // 层级选择（如教材同步词汇）
      this.setData({
        selectedCategory: category,
        selectedLibrary: null,
        selectedVolume: null,
        selectedUnits: [],
        currentStage: 'library'
      });
    } else {
      // 直接选择词库
      this.setData({
        selectedCategory: category,
        selectedLibrary: null,
        selectedVolume: null,
        selectedUnits: [],
        currentStage: 'library'
      });
    }
  },

  // 选择具体词库
  onLibrarySelect(e) {
    const libraryId = e.currentTarget.dataset.libraryId;
    const category = this.data.selectedCategory;
    const library = this.data.libraries[category.id].find(l => l.id === libraryId);
    
    console.log('选择词库:', library);

    if (category.id === 'textbook') {
      // 教材同步词汇，需要选择册数
      this.setData({
        selectedLibrary: library,
        selectedVolume: null,
        selectedUnits: [],
        currentStage: 'volume'
      });
    } else {
      // 其他词库，可以直接确认
      this.setData({
        selectedLibrary: library,
        selectedVolume: null,
        selectedUnits: []
      });
      this.confirmSelection();
    }
  },

  // 选择册数
  onVolumeSelect(e) {
    const volumeId = e.currentTarget.dataset.volumeId;
    const publisherId = this.data.selectedLibrary.id;
    const volume = this.data.volumes[publisherId].find(v => v.id === volumeId);
    
    console.log('选择册数:', volume);

    this.setData({
      selectedVolume: volume,
      currentStage: 'unit',
      selectedUnits: [] // 清空之前的单元选择
    });

    // 这里可以根据实际需要加载对应册数的单元数据
    this.loadUnitsForVolume(publisherId, volumeId);
  },

  // 加载册数对应的单元数据
  async loadUnitsForVolume(publisherId, volumeId) {
    // 从常量文件获取单元数据
    const basicUnits = TEXTBOOK_UNITS[publisherId] && TEXTBOOK_UNITS[publisherId][volumeId] 
      ? TEXTBOOK_UNITS[publisherId][volumeId]
      : [];

    if (basicUnits.length === 0) {
      this.setData({
        units: [],
        selectedUnits: []
      });
      return;
    }

    // 先设置基本单元数据，词汇数量显示为加载中
    const unitsWithSelection = basicUnits.map(unit => ({
      id: unit.id,
      name: unit.name,
      wordCount: -1, // -1表示加载中
      isSelected: false
    }));

    this.setData({
      units: unitsWithSelection,
      selectedUnits: []
    });

    // 获取对应的库ID
    const libraryId = `${publisherId}_${volumeId}`;
    
    // 并行获取每个单元的词汇数量
    try {
      const countPromises = basicUnits.map(unit => 
        this.getUnitWordCount(libraryId, unit.id)
      );
      
      const wordCounts = await Promise.all(countPromises);
      
      // 更新单元数据，包含实际词汇数量
      const updatedUnits = basicUnits.map((unit, index) => ({
        id: unit.id,
        name: unit.name,
        wordCount: wordCounts[index] || 0,
        isSelected: false
      }));

      this.setData({
        units: updatedUnits
      });
      
    } catch (error) {
      console.error('获取单元词汇数量失败:', error);
      // 出错时设置为0
      const errorUnits = basicUnits.map(unit => ({
        id: unit.id,
        name: unit.name,
        wordCount: 0,
        isSelected: false
      }));

      this.setData({
        units: errorUnits
      });
    }
  },

  // 获取单个单元的词汇数量
  async getUnitWordCount(libraryId, unitId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getWords',
        data: {
          libraryId: libraryId,
          unit: unitId,
          getTotalCount: true,
          limit: 1 // 只需要获取总数，不需要具体词汇
        }
      });
      
      if (result.result.code === 200) {
        return result.result.totalCount || 0;
      } else {
        console.error('获取词汇数量失败:', result.result.message);
        return 0;
      }
    } catch (error) {
      console.error('调用getWords云函数失败:', error);
      return 0;
    }
  },

  // 切换单元选择
  onUnitToggle(e) {
    const unitId = e.currentTarget.dataset.unitId;
    const selectedUnits = [...this.data.selectedUnits];
    const units = [...this.data.units];
    
    // 找到对应的单元并切换选择状态
    const unitIndex = units.findIndex(unit => unit.id === unitId);
    if (unitIndex !== -1) {
      units[unitIndex].isSelected = !units[unitIndex].isSelected;
      
      if (units[unitIndex].isSelected) {
        // 添加到选中列表
        if (!selectedUnits.includes(unitId)) {
          selectedUnits.push(unitId);
        }
      } else {
        // 从选中列表移除
        const index = selectedUnits.indexOf(unitId);
        if (index > -1) {
          selectedUnits.splice(index, 1);
        }
      }
    }
    
    this.setData({
      units: units,
      selectedUnits: selectedUnits
    });
  },

  // 全选单元
  selectAllUnits() {
    const units = this.data.units.map(unit => ({
      ...unit,
      isSelected: true
    }));
    const allUnitIds = units.map(unit => unit.id);
    
    this.setData({
      units: units,
      selectedUnits: allUnitIds
    });
  },

  // 清空单元选择
  clearUnitSelection() {
    const units = this.data.units.map(unit => ({
      ...unit,
      isSelected: false
    }));
    
    this.setData({
      units: units,
      selectedUnits: []
    });
  },

  // 选择自定义词库
  onCustomWordbankSelect(e) {
    const wordbankId = e.currentTarget.dataset.wordbankId;
    const wordbank = this.data.customWordbanks.find(wb => wb.id === wordbankId);
    
    console.log('选择自定义词库:', wordbank);

    // 保存选择并返回
    const app = getApp();
    app.globalData.selectedLibrary = {
      id: wordbankId,
      name: wordbank.name,
      isCustom: true,
      testOrder: this.data.testOrder
    };

    this.returnToTestPage();
  },

  // 切换测试顺序
  onTestOrderChange(e) {
    const order = e.currentTarget.dataset.order;
    this.setData({
      testOrder: order
    });
  },

  // 返回上一级
  goBack() {
    const { currentStage } = this.data;
    
    switch (currentStage) {
      case 'category':
        // 在分类选择阶段，直接返回上一页
        wx.navigateBack({
          fail: () => {
            // 如果返回失败，可能是通过其他方式进入的，返回到单词检测页面
            const { returnTo } = this.data;
            if (returnTo === 'wordtest') {
              wx.redirectTo({
                url: '/pages/wordtest/wordtest'
              });
            } else if (returnTo === 'phrasetest') {
              wx.redirectTo({
                url: '/pages/phrasetest/phrasetest'
              });
            } else {
              wx.switchTab({
                url: '/pages/index/index'
              });
            }
          }
        });
        break;
      case 'library':
      case 'custom':
        this.setData({
          currentStage: 'category',
          selectedCategory: null,
          selectedLibrary: null,
          selectedVolume: null,
          selectedUnits: []
        });
        break;
      case 'volume':
        this.setData({
          currentStage: 'library',
          selectedLibrary: null,
          selectedVolume: null,
          selectedUnits: []
        });
        break;
      case 'unit':
        this.setData({
          currentStage: 'volume',
          selectedVolume: null,
          selectedUnits: []
        });
        break;
      default:
        wx.navigateBack();
    }
  },

  // 确认选择
  confirmSelection() {
    const { selectedCategory, selectedLibrary, selectedVolume, selectedUnits, testOrder, units } = this.data;
    
    let libraryConfig = {
      testOrder: testOrder
    };

    if (selectedCategory.id === 'textbook') {
      // 教材同步词汇
      if (selectedUnits.length === 0) {
        wx.showToast({
          title: '请选择至少一个单元',
          icon: 'none'
        });
        return;
      }

      // 使用新的ID格式：renjiao_bixiu1 或 beishi_bixiu1
      const realLibraryId = selectedVolume.realId;
      
      // 构建显示名称
      let displayName = '';
      const totalUnits = units.length; // 总单元数
      const selectedCount = selectedUnits.length; // 选中单元数
      
      if (selectedCount === 1) {
        // 单个单元：显示到单元级别
        const selectedUnit = units.find(u => u.id === selectedUnits[0]);
        displayName = `${selectedLibrary.name}${selectedVolume.name} - ${selectedUnit?.name || selectedUnits[0]}`;
      } else if (selectedCount === totalUnits) {
        // 选择了全部单元：按册显示
        displayName = `${selectedLibrary.name}${selectedVolume.name}`;
      } else {
        // 多个但不是全部：显示单元简写列表
        // 按照units数组的顺序排序，确保显示顺序正确
        const sortedSelectedUnits = units
          .filter(unit => selectedUnits.includes(unit.id))
          .map(unit => {
            // 如果是Welcome Unit，简写为Welcome
            if (unit.name.toLowerCase().includes('welcome')) {
              return 'Welcome';
            }
            // 如果是Unit1, Unit2格式，简写为U1, U2
            const match = unit.name.match(/Unit\s*(\d+)/i);
            if (match) {
              return `U${match[1]}`;
            }
            // 其他情况保持原名
            return unit.name;
          });
        
        const unitNames = sortedSelectedUnits.join(',');
        displayName = `${selectedLibrary.name}${selectedVolume.name} - ${unitNames}`;
      }
      
      libraryConfig = {
        type: 'textbook',
        publisher: selectedLibrary.id,
        publisherName: selectedLibrary.name,
        volume: selectedVolume.id,
        volumeName: selectedVolume.name,
        units: selectedUnits,
        testOrder: testOrder,
        id: realLibraryId,
        name: displayName,
        // 如果只选择了一个单元，单独记录
        unit: selectedUnits.length === 1 ? selectedUnits[0] : '',
        unitName: selectedUnits.length === 1 ? this.data.units.find(u => u.id === selectedUnits[0])?.name || '' : ''
      };
    } else {
      // 其他词库
      libraryConfig = {
        type: selectedCategory.id,
        id: selectedLibrary.id,
        name: selectedLibrary.name,
        testOrder: testOrder
      };
    }

    console.log('确认选择:', libraryConfig);

    // 保存到全局数据
    const app = getApp();
    app.globalData.selectedLibrary = libraryConfig;

    this.returnToTestPage();
  },

  // 返回测试页面
  returnToTestPage() {
    const { returnTo } = this.data;
    
    wx.navigateBack({
      success: () => {
        wx.showToast({
          title: '词库选择成功',
          icon: 'success'
        });
      }
    });
  }
}); 