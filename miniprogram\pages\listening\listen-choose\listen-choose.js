Page({
  data: {
    currentSection: 1, // 1: 第一节, 2: 第二节
    currentQuestion: 1,
    totalQuestions: 14,
    section1Questions: 4, // 第一节4题
    section2Questions: 10, // 第二节10题
    
    // 当前题目数据
    currentQuestionData: null,
    
    // 第一节题目（每段对话1题）
    section1Data: [
      {
        id: 1,
        question: "Why was Susan late for work?",
        options: [
          { key: 'A', text: 'She got up a bit late' },
          { key: 'B', text: 'She missed the train' },
          { key: 'C', text: 'She started too late' }
        ],
        correctAnswer: 'B',
        audioText: "W: Were you late for work this morning?\nM: Yeah, but I was just 10 minutes late. I got up a bit late.\nW: Did <PERSON> say anything to you?\nM: No, she didn't catch the train and was late too.\nW: Well, you were really lucky. But you'd better start earlier tomorrow.",
        listenTimes: 1, // 第一节听一遍
        readTime: 5, // 阅读时间5秒
        answerTime: 5 // 作答时间5秒
      },
      {
        id: 2,
        question: "What does the woman do probably?",
        options: [
          { key: 'A', text: "She's a teacher." },
          { key: 'B', text: "She's a news reporter" },
          { key: 'C', text: "She's an exchange student." }
        ],
        correctAnswer: 'A',
        audioText: "M: Do you read any newspapers?\nW: Yeah, I often read The New York Times and New Yorker. I need to get some information to prepare for my classes.\nM: Do you find them helpful?\nW: Sure. I can often get some new ideas, and I often exchange those ideas with my students.",
        listenTimes: 1,
        readTime: 5,
        answerTime: 5
      },
      {
        id: 3,
        question: "What does Donna plan to do after graduation?",
        options: [
          { key: 'A', text: 'Work as a teacher in China.' },
          { key: 'B', text: 'Find a job in her town.' },
          { key: 'C', text: 'Start her own business' }
        ],
        correctAnswer: 'B',
        audioText: "W: I heard that you've quitted the business you started with your friends. Why?\nM: I've applied for a teaching job in China. I want to go there after graduation.\nW: You're more adventurous than Donna. She never gives up the idea of taking a job in an organization in our town.\nM: Well, most of her friends and family members are here after all.",
        listenTimes: 1,
        readTime: 5,
        answerTime: 5
      },
      {
        id: 4,
        question: "What can we learn from the conversation?",
        options: [
          { key: 'A', text: "Nancy doesn't want to go on a business trip." },
          { key: 'B', text: 'Nancy is now on vacation on the beach.' },
          { key: 'C', text: 'The two speakers are workmates.' }
        ],
        correctAnswer: 'C',
        audioText: "W: Have you heard that our boss wants Nancy to go on a business trip to Acapulco?\nM: I bet she won't decline.\nW: Sure. She'll get to spend her free time bathing in the sun on those lovely beaches.\nM: When will she be leaving?\nW: Probably in a week.",
        listenTimes: 1,
        readTime: 5,
        answerTime: 5
      }
    ],
    
    // 第二节题目（每段对话或独白2题）
    section2Data: [
      {
        id: 5,
        groupId: 1,
        question: "How many senior classes are there in the school?",
        options: [
          { key: 'A', text: '22' },
          { key: 'B', text: '24' },
          { key: 'C', text: '30' }
        ],
        correctAnswer: 'C',
        audioText: "W: Hi, Mr. Li. Could you give us a brief introduction to your school?\nM: Sure. Our school is a public high school. There are 30 senior and 24 junior classes with over 3,000 students. And there are 220 teachers, including 2 foreign teachers.\nW: Where are the foreign teachers from?\nM: One is from America, the other is from Canada.\nW: What do they teach?\nM: The American lady gives spoken English classes. They are very interesting. The Canadian lady gives lectures on the culture and society of English-speaking countries.\nW: I see. Thank you.",
        listenTimes: 2, // 第二节听两遍
        readTime: 10, // 两题一起读10秒
        answerTime: 5
      },
      {
        id: 6,
        groupId: 1,
        question: "Where is the oral English teacher from?",
        options: [
          { key: 'A', text: 'Canada' },
          { key: 'B', text: 'America' },
          { key: 'C', text: 'England' }
        ],
        correctAnswer: 'B',
        audioText: "", // 同上一题
        listenTimes: 2,
        readTime: 0, // 已经读过了
        answerTime: 5
      }
      // 其他题目数据...
    ],
    
    // 答题状态
    selectedAnswer: '',
    isAnswered: false,
    showResult: false,
    isCorrect: false,
    
    // 时间控制
    readTimeLeft: 0,
    answerTimeLeft: 0,
    isReading: false,
    isAnswering: false,
    
    // 音频控制
    audioPlayed: 0,
    canPlayAudio: false,
    isPlayingAudio: false,
    
    // 结果统计
    correctCount: 0,
    totalScore: 0,
    
    // 流程控制
    currentStep: 'instruction', // instruction, reading, audio, answering, result
    
    timer: null
  },

  onLoad() {
    this.initQuestion();
  },

  onUnload() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  // 初始化题目
  initQuestion() {
    const { currentSection, currentQuestion } = this.data;
    let questionData;
    
    if (currentSection === 1) {
      questionData = this.data.section1Data[currentQuestion - 1];
    } else {
      // 第二节的题目索引需要调整
      const section2Index = currentQuestion - this.data.section1Questions - 1;
      questionData = this.data.section2Data[section2Index];
    }
    
    this.setData({
      currentQuestionData: questionData,
      selectedAnswer: '',
      isAnswered: false,
      showResult: false,
      audioPlayed: 0,
      currentStep: 'instruction'
    });
    
    // 显示题目说明
    this.showInstruction();
  },

  // 显示题目说明
  showInstruction() {
    const { currentSection, currentQuestion } = this.data;
    let instruction = '';
    
    if (currentSection === 1) {
      instruction = `听下面一段对话，回答第${currentQuestion}小题。现在，你有5秒钟的时间阅读该小题。`;
    } else {
      // 第二节的说明
      if (currentQuestion === 5) {
        instruction = `听下面一段对话，回答第5至第6小题。现在，你有10秒钟的时间阅读这两道小题。`;
      } else if (currentQuestion === 7) {
        instruction = `听下面一段对话，回答第7至第8小题。现在，你有10秒钟的时间阅读这两道小题。`;
      }
      // ... 其他题目的说明
    }
    
    wx.showModal({
      title: '题目说明',
      content: instruction,
      showCancel: false,
      confirmText: '开始阅读',
      success: () => {
        this.startReading();
      }
    });
  },

  // 开始阅读题目
  startReading() {
    const { currentQuestionData } = this.data;
    
    this.setData({
      currentStep: 'reading',
      isReading: true,
      readTimeLeft: currentQuestionData.readTime
    });
    
    this.startTimer('read');
  },

  // 开始播放音频
  startAudio() {
    this.setData({
      currentStep: 'audio',
      canPlayAudio: true,
      isPlayingAudio: true
    });
    
    // 模拟音频播放
    setTimeout(() => {
      this.setData({
        audioPlayed: this.data.audioPlayed + 1,
        isPlayingAudio: false
      });
      
      // 检查是否需要播放第二遍
      const { currentQuestionData, audioPlayed } = this.data;
      if (audioPlayed < currentQuestionData.listenTimes) {
        setTimeout(() => {
          this.startAudio();
        }, 1000);
      } else {
        this.startAnswering();
      }
    }, 3000); // 模拟3秒音频
  },

  // 开始答题
  startAnswering() {
    const { currentQuestionData } = this.data;
    
    this.setData({
      currentStep: 'answering',
      isAnswering: true,
      answerTimeLeft: currentQuestionData.answerTime
    });
    
    this.startTimer('answer');
  },

  // 计时器
  startTimer(type) {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    
    const timer = setInterval(() => {
      if (type === 'read') {
        const timeLeft = this.data.readTimeLeft - 1;
        this.setData({ readTimeLeft: timeLeft });
        
        if (timeLeft <= 0) {
          clearInterval(timer);
          this.setData({ isReading: false });
          this.startAudio();
        }
      } else if (type === 'answer') {
        const timeLeft = this.data.answerTimeLeft - 1;
        this.setData({ answerTimeLeft: timeLeft });
        
        if (timeLeft <= 0) {
          clearInterval(timer);
          this.setData({ isAnswering: false });
          this.submitAnswer();
        }
      }
    }, 1000);
    
    this.setData({ timer });
  },

  // 选择答案
  selectAnswer(e) {
    const answer = e.currentTarget.dataset.answer;
    this.setData({
      selectedAnswer: answer
    });
  },

  // 提交答案
  submitAnswer() {
    const { selectedAnswer, currentQuestionData } = this.data;
    const isCorrect = selectedAnswer === currentQuestionData.correctAnswer;
    
    // 如果答错，记录错题
    if (!isCorrect) {
      this.addToMistakeBook({
        questionData: currentQuestionData,
        userAnswer: selectedAnswer,
        correctAnswer: currentQuestionData.correctAnswer
      });
    }
    
    this.setData({
      isAnswered: true,
      showResult: true,
      isCorrect,
      correctCount: isCorrect ? this.data.correctCount + 1 : this.data.correctCount,
      totalScore: isCorrect ? this.data.totalScore + 1.5 : this.data.totalScore
    });
  },

  // 查看听力原文
  showAudioText() {
    const { currentQuestionData } = this.data;
    wx.showModal({
      title: '听力原文',
      content: currentQuestionData.audioText,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 下一题
  nextQuestion() {
    const { currentQuestion, totalQuestions } = this.data;
    
    if (currentQuestion < totalQuestions) {
      let newQuestion = currentQuestion + 1;
      let newSection = this.data.currentSection;
      
      // 检查是否进入第二节
      if (newQuestion > this.data.section1Questions && newSection === 1) {
        newSection = 2;
        wx.showModal({
          title: '进入第二节',
          content: '第二节，听下面五段对话或独白，每段对话或独白后有两道小题，从每题所给的A、B、C三个选项中选出最佳选项。听每段对话或独白前，你将有5秒钟的时间阅读每小题。听完后，每小题将有5秒钟的作答时间。每段对话或独白你将听两遍。',
          showCancel: false,
          confirmText: '开始第二节',
          success: () => {
            this.setData({
              currentQuestion: newQuestion,
              currentSection: newSection
            });
            this.initQuestion();
          }
        });
        return;
      }
      
      this.setData({
        currentQuestion: newQuestion,
        currentSection: newSection
      });
      this.initQuestion();
    } else {
      this.showFinalResult();
    }
  },

  // 显示最终结果
  showFinalResult() {
    const { correctCount, totalScore } = this.data;
    
    wx.showModal({
      title: '练习完成',
      content: `恭喜完成听后选择练习！\n正确题数：${correctCount}/14\n得分：${totalScore}/21分`,
      showCancel: false,
      confirmText: '返回',
      success: () => {
        wx.navigateBack();
      }
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 添加错题到错题本
  addToMistakeBook(mistakeData) {
    const app = getApp();
    
    // 使用更可靠的登录状态检查
    if (!app.canCollectMistakes()) {
      console.log('用户未登录或openid不可用，跳过错题收集');
      return;
    }
    
    const userId = app.getUserOpenId();

    // 构建错题数据
    const mistakeRecord = {
      question: mistakeData.questionData.question,
      options: mistakeData.questionData.options.map(opt => `${opt.key}. ${opt.text}`).join('\n'),
      audioText: mistakeData.questionData.audioText,
      userAnswer: mistakeData.userAnswer,
      correctAnswer: mistakeData.correctAnswer,
      mistakeType: 'listening_choose', // 标识来源为听后选择
      section: this.data.currentSection,
      questionId: mistakeData.questionData.id,
      createTime: new Date()
    };

    wx.cloud.callFunction({
      name: 'addMistake',
      data: {
        userId: userId,
        wordId: `listen_choose_${mistakeData.questionData.id}`,
        type: 'listening', // 归类到听口错题本
        extra: mistakeRecord
      }
    }).then(result => {
      console.log('听后选择错题已自动添加到错题本:', result);
    }).catch(error => {
      console.error('自动添加错题失败:', error);
      // 静默失败，不影响练习流程
    });
  }
}); 