/* 听后记录转述页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #667eea;
  border-radius: 50%;
}

.back-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.title-info {
  flex: 1;
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 8rpx;
}

.score-info {
  min-width: 120rpx;
  text-align: right;
}

.score-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
}

/* 流程指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  padding: 0 40rpx;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #ddd;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.step.active .step-number {
  background: #667eea;
  color: white;
}

.step.completed .step-number {
  background: #52c41a;
  color: white;
}

.step-label {
  font-size: 24rpx;
  color: #666;
}

.step.active .step-label {
  color: #667eea;
  font-weight: bold;
}

.step.completed .step-label {
  color: #52c41a;
  font-weight: bold;
}

.step-line {
  width: 100rpx;
  height: 4rpx;
  background: #ddd;
  margin: 0 20rpx;
  margin-bottom: 40rpx;
}

.step.completed + .step-line {
  background: #52c41a;
}

/* 通用卡片样式 */
.instruction-card, .table-container, .listening-card, .recording-card, .result-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

/* 时间显示 */
.timer-display {
  text-align: center;
  margin-bottom: 30rpx;
}

.timer-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
  padding: 20rpx 40rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 50rpx;
}

/* 说明卡片 */
.instruction-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.instruction-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 表格样式 */
.table-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e0e0e0;
}

.table-content {
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  overflow: hidden;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #e0e0e0;
}

.table-row:last-child {
  border-bottom: none;
}

.header-row {
  background: #f5f5f5;
  font-weight: bold;
}

.cell {
  padding: 20rpx;
  border-right: 1rpx solid #e0e0e0;
}

.cell:last-child {
  border-right: none;
}

.step-cell {
  width: 120rpx;
  text-align: center;
  background: rgba(102, 126, 234, 0.05);
  font-weight: bold;
  color: #667eea;
}

.content-cell {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.6;
}

.content-text {
  color: #333;
}

/* 填空输入框 */
.content-with-blank {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10rpx;
}

.blank-input {
  min-width: 120rpx;
  padding: 8rpx 15rpx;
  border: 2rpx solid #667eea;
  border-radius: 8rpx;
  background: #f8f9ff;
  font-size: 26rpx;
  text-align: center;
  color: #333;
}

.blank-input:focus {
  border-color: #4c63d2;
  background: white;
}

/* 听力播放 */
.listening-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.listening-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.play-status {
  font-size: 26rpx;
  color: #667eea;
  font-weight: bold;
}

.audio-visual {
  text-align: center;
  margin-bottom: 30rpx;
  position: relative;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.audio-visual.playing {
  animation: pulse 2s infinite;
}

.wave-container {
  display: flex;
  align-items: center;
  gap: 8rpx;
  position: absolute;
}

.wave {
  width: 6rpx;
  height: 40rpx;
  background: #667eea;
  border-radius: 3rpx;
  animation: wave 1.2s ease-in-out infinite;
}

.wave1 { animation-delay: 0s; }
.wave2 { animation-delay: 0.2s; }
.wave3 { animation-delay: 0.4s; }
.wave4 { animation-delay: 0.6s; }

@keyframes wave {
  0%, 50%, 100% { height: 20rpx; opacity: 0.5; }
  25% { height: 60rpx; opacity: 1; }
}

.audio-icon {
  font-size: 60rpx;
  position: absolute;
  z-index: 2;
}

.listening-tips {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  font-style: italic;
}

/* 填空操作 */
.filling-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

/* 转述准备 */
.retelling-start, .retelling-prompt {
  background: #f8f9ff;
  border-radius: 12rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
  border-left: 4rpx solid #667eea;
}

.start-title, .prompt-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.start-text, .prompt-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  font-style: italic;
}

.preparation-tips {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12rpx;
  padding: 25rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

/* 录音显示 */
.recording-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.record-time {
  text-align: right;
}

.record-text {
  font-size: 28rpx;
  color: #666;
  padding: 15rpx 25rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 25rpx;
}

/* 录音卡片 */
.recording-icon {
  font-size: 80rpx;
  text-align: center;
  margin-bottom: 20rpx;
  animation: pulse 2s infinite;
}

.recording-icon.recording {
  animation: recording-pulse 1s infinite;
}

.recording-status {
  text-align: center;
}

.status-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

/* 控制按钮 */
.recording-controls {
  text-align: center;
  margin-top: 30rpx;
}

.control-btn {
  padding: 25rpx 60rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.stop-btn {
  background: #ff4d4f;
  color: white;
}

/* 操作按钮 */
.action-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background: #667eea;
  color: white;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #333;
}

/* 结果页面 */
.result-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.result-icon {
  font-size: 100rpx;
  display: block;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.score-breakdown {
  margin-bottom: 40rpx;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.score-label {
  font-size: 30rpx;
  color: #333;
}

.score-value {
  font-size: 30rpx;
  font-weight: bold;
  color: #667eea;
}

.score-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-top: 2rpx solid #667eea;
  margin-top: 20rpx;
}

.total-label {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.total-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
}

.result-actions {
  display: flex;
  gap: 15rpx;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes recording-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

/* 填空表格容器 */
.filling-table-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid #667eea;
}

/* 填空状态显示 */
.filling-status {
  text-align: center;
  padding: 25rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 15rpx;
  margin-top: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.3);
}

.status-text {
  font-size: 30rpx;
  color: #667eea;
  font-weight: bold;
}

/* 已填答案显示 */
.filled-answers {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.answers-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.answer-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.answer-item {
  display: flex;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #f8f9ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.answer-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #667eea;
  margin-right: 15rpx;
  min-width: 40rpx;
}

.answer-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 禁用状态的输入框 */
.blank-input:disabled {
  background: #f0f0f0;
  color: #999;
  border-color: #ddd;
}

/* 倒计时显示 */
.countdown-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.countdown-circle {
  width: 160rpx;
  height: 160rpx;
  border: 8rpx solid #667eea;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  margin-bottom: 20rpx;
  animation: countdown-pulse 1s infinite;
}

.countdown-number {
  font-size: 60rpx;
  font-weight: bold;
  color: #667eea;
  line-height: 1;
}

.countdown-unit {
  font-size: 24rpx;
  color: #667eea;
  margin-top: 5rpx;
}

.countdown-text {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

@keyframes countdown-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 标准表格样式 */
.standard-table {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.table-wrapper {
  border: 2rpx solid #333;
  border-radius: 8rpx;
  overflow: hidden;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #333;
}

.table-row:last-child {
  border-bottom: none;
}

.table-header {
  background: #f0f0f0;
  font-weight: bold;
}

.table-cell {
  padding: 20rpx 15rpx;
  border-right: 1rpx solid #333;
  font-size: 26rpx;
  line-height: 1.5;
}

.table-cell:last-child {
  border-right: none;
}

.step-column {
  width: 120rpx;
  text-align: center;
  background: rgba(240, 240, 240, 0.5);
  font-weight: bold;
}

.content-column {
  flex: 1;
  text-align: left;
}

/* 答案输入区域 */
.answer-input-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.answer-input-section.active {
  border: 2rpx solid #667eea;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.2);
}

.input-row {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.input-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
}

.input-label {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.input-line {
  width: 100%;
  height: 4rpx;
  background: #333;
  border-radius: 2rpx;
  position: relative;
}

.input-line::before {
  content: '';
  position: absolute;
  top: -15rpx;
  left: 0;
  right: 0;
  height: 30rpx;
}

.answer-input {
  width: 100%;
  padding: 15rpx 10rpx;
  border: 2rpx solid #667eea;
  border-radius: 8rpx;
  background: white;
  font-size: 28rpx;
  text-align: center;
  color: #333;
}

.answer-input:focus {
  border-color: #4c63d2;
  box-shadow: 0 0 10rpx rgba(102, 126, 234, 0.3);
}

.answer-input:disabled {
  background: #f0f0f0;
  color: #999;
  border-color: #ddd;
}

.answer-input::placeholder {
  color: #999;
  font-size: 24rpx;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .input-row {
    flex-wrap: wrap;
  }
  
  .input-item {
    width: 48%;
    margin-bottom: 20rpx;
  }
} 