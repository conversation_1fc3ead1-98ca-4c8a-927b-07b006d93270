const app = getApp();

Page({
  data: {
    wordbankId: '',
    wordbank: null,
    words: [],
    loading: true,
    error: '',
    selectedWords: [],
    mode: 'view', // view-查看模式, select-选择模式
    returnTo: '',
    testMode: '' // 预设的测试模式
  },

  onLoad(options) {
    const { wordbankId, mode, returnTo, testMode } = options;
    
    if (!wordbankId) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      wordbankId: wordbankId,
      mode: mode || 'view',
      returnTo: returnTo || '',
      testMode: testMode || '' // 保存测试模式
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '自定义词库'
    });

    // 加载词库数据
    this.loadWordbankData();
  },

  // 加载词库数据
  async loadWordbankData() {
    this.setData({ loading: true, error: '' });

    try {
      const db = wx.cloud.database();
      
      // 获取当前用户信息
      const userInfo = await app.getUserInfo();
      if (!userInfo || !userInfo.openid) {
        throw new Error('用户未登录');
      }

      // 查询自定义词库
      const result = await db.collection('custom_wordbanks')
        .where({
          id: this.data.wordbankId,
          creatorOpenid: userInfo.openid
        })
        .get();

      if (result.data.length === 0) {
        throw new Error('词库不存在或无权访问');
      }

      const wordbank = result.data[0];
      const words = wordbank.words || [];

      // 为每个词汇添加必要的字段以兼容学习页面
      const formattedWords = words.map((word, index) => ({
        _id: `${this.data.wordbankId}_${index}`,
        word: word.word,
        words: word.word, // 兼容字段
        phonetic: word.phonetic || '',
        chinese: word.chinese || '',
        meaning: word.chinese || '', // 兼容字段
        example: word.example || '',
        selected: false,
        // 兼容学习页面的数据结构
        meanings: [{
          partOfSpeech: 'n.',
          definitions: [{
            definition: word.chinese || '',
            example: word.example || ''
          }]
        }]
      }));

      this.setData({
        wordbank: wordbank,
        words: formattedWords,
        loading: false
      });

      // 更新页面标题
      wx.setNavigationBarTitle({
        title: wordbank.name || '自定义词库'
      });

    } catch (error) {
      console.error('加载词库数据失败:', error);
      this.setData({
        loading: false,
        error: error.message || '加载失败'
      });
    }
  },

  // 选择/取消选择词汇
  onWordSelect(e) {
    if (this.data.mode !== 'select') return;

    const { index } = e.currentTarget.dataset;
    const words = [...this.data.words];
    const word = words[index];
    
    word.selected = !word.selected;
    
    // 更新选中的词汇列表
    const selectedWords = words.filter(w => w.selected);
    
    this.setData({
      words: words,
      selectedWords: selectedWords
    });
  },

  // 全选
  selectAll() {
    const words = this.data.words.map(word => ({
      ...word,
      selected: true
    }));
    
    this.setData({
      words: words,
      selectedWords: words
    });
  },

  // 清空选择
  clearSelection() {
    const words = this.data.words.map(word => ({
      ...word,
      selected: false
    }));
    
    this.setData({
      words: words,
      selectedWords: []
    });
  },

  // 开始学习/测试
  startLearning() {
    if (this.data.selectedWords.length === 0) {
      wx.showToast({
        title: '请选择要学习的词汇',
        icon: 'none'
      });
      return;
    }

    // 存储学习数据到全局
    app.globalData.learningData = {
      words: this.data.selectedWords,
      libraryId: this.data.wordbankId,
      libraryName: this.data.wordbank.name,
      isCustom: true
    };

    // 根据返回来源跳转到相应页面
    if (this.data.returnTo === 'wordtest') {
      // 如果有预设的测试模式，直接使用
      if (this.data.testMode) {
        this.navigateToLearningPage(this.data.testMode);
      } else {
        // 否则显示选择菜单
        wx.showActionSheet({
          itemList: ['英译汉', '汉译英', '单词听写', '单词消消乐'],
          success: (res) => {
            const modes = ['en_to_cn', 'cn_to_en', 'dictation', 'elimination'];
            const selectedMode = modes[res.tapIndex];
            
            // 跳转到对应的学习页面
            this.navigateToLearningPage(selectedMode);
          }
        });
      }
    } else {
      // 默认跳转到学习页面
      wx.navigateTo({
        url: '/pages/learning/learning'
      });
    }
  },

  // 跳转到学习页面
  navigateToLearningPage(mode) {
    let url = '';
    
    switch (mode) {
      case 'en_to_cn':
      case 'cn_to_en':
        url = `/pages/test/test?mode=${mode}`;
        break;
      case 'dictation':
        url = '/pages/listening/listening';
        break;
      case 'elimination':
        url = '/pages/task/puzzle/puzzle';
        break;
      default:
        url = '/pages/learning/learning';
    }

    wx.navigateTo({
      url: url,
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 编辑词库
  editWordbank() {
    wx.navigateTo({
      url: `/pages/wordbank/edit-wordbank/edit-wordbank?wordbankId=${this.data.wordbankId}`
    });
  },

  // 删除词库
  deleteWordbank() {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除词库"${this.data.wordbank?.name || '未知词库'}"吗？此操作不可恢复。`,
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '删除中...',
              mask: true
            });

            const db = wx.cloud.database();
            
            // 删除词库
            await db.collection('custom_wordbanks')
              .where({
                id: this.data.wordbankId
              })
              .remove();

            wx.hideLoading();
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });

            // 返回上一页
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);

          } catch (error) {
            wx.hideLoading();
            console.error('删除词库失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 分享词库
  onShareAppMessage() {
    return {
      title: `我创建了词库「${this.data.wordbank?.name || '自定义词库'}」，快来一起学习吧！`,
      path: `/pages/wordbank/custom-wordlist/custom-wordlist?wordbankId=${this.data.wordbankId}&shared=true`
    };
  }
}); 