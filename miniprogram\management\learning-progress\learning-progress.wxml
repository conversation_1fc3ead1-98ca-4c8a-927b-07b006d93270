<!--pages/profile/learning-progress/learning-progress.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-content" bindlongpress="resetDeletedItems">
      <text class="page-title">学习进度</text>
      <text class="page-desc">查看各词库的学习进度</text>
    </view>
    
    <!-- 操作按钮 -->
    <view class="header-actions" wx:if="{{!empty && !loading}}">
      <button class="action-btn edit-btn" bindtap="toggleEditMode">
        {{editMode ? '取消' : '编辑'}}
      </button>
    </view>
  </view>

  <!-- 编辑模式工具栏 -->
  <view class="edit-toolbar" wx:if="{{editMode && !empty}}">
    <button class="toolbar-btn select-all-btn" bindtap="toggleSelectAll">
      全选
    </button>
    <button class="toolbar-btn delete-btn" bindtap="batchDelete">
      删除选中
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-content">
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{empty}}">
    <view class="empty-content">
      <text class="empty-icon">📚</text>
      <text class="empty-title">暂无学习进度</text>
      <text class="empty-desc">开始学习词库后，进度会在这里显示</text>
      <button class="empty-btn" bindtap="goToWordbank">去学习</button>
    </view>
  </view>

  <!-- 进度列表 -->
  <view class="progress-list" wx:else>
    <view class="progress-item {{editMode ? 'edit-mode' : ''}}" wx:for="{{progressList}}" wx:key="key">
      <!-- 选择框 -->
      <view class="select-checkbox" wx:if="{{editMode}}" bindtap="toggleItemSelect" data-key="{{item.key}}">
        <view class="checkbox {{item.isSelected ? 'checked' : ''}}">
          <text class="check-icon" wx:if="{{item.isSelected}}">✓</text>
        </view>
      </view>
      
      <view class="progress-content">
        <view class="progress-header">
          <view class="library-info">
            <text class="library-name">{{item.libraryName}}</text>
            <text class="mode-tag">{{item.modeText}}</text>
          </view>
          <view class="progress-stats">
            <text class="progress-percent">{{item.percentage}}%</text>
            <text class="last-study-time">{{item.lastStudyTimeText}}</text>
          </view>
        </view>
        
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{item.percentage}}%"></view>
        </view>
        
        <view class="progress-details">
          <text class="progress-text" wx:if="{{!item.isGrouped}}">{{item.currentIndex}} / {{item.totalCount}} 个词汇</text>
          <text class="progress-text" wx:if="{{item.isGrouped}}">{{item.progressText}}</text>
          <text class="detail-text" wx:if="{{item.isGrouped && item.detailText}}">{{item.detailText}}</text>
        </view>
        
        <view class="progress-actions" wx:if="{{!editMode}}">
          <button class="action-btn continue-btn" bindtap="continueStudy" data-item="{{item}}">
            {{item.isDemo ? '开始学习' : '继续学习'}}
          </button>
          <button class="action-btn view-btn" bindtap="viewLibrary" data-item="{{item}}">
            查看词库
          </button>
          <button class="action-btn clear-btn" bindtap="clearProgress" data-item="{{item}}">
            删除
          </button>
        </view>
      </view>
    </view>
  </view>
</view> 