<!--pages/profile/share-management/share-management.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>
  
  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 分享信息头部 -->
    <view class="share-header">
      <view class="share-title">
        <text class="library-name">{{shareData.libraryName}}</text>
        <text class="test-mode">{{shareData.testType === 'en_to_cn' ? '英译汉' : '汉译英'}}</text>
      </view>
      <view class="share-meta">
        <text class="share-id">ID: {{shareData.shareId}}</text>
        <text class="expire-time">有效期至: {{shareData.expireTime}}</text>
      </view>
      <view wx:if="{{isMultiLevel}}" class="multi-level-info">
        <text class="level-info">多关卡任务 (共{{totalLevels}}关)</text>
      </view>
    </view>
    
    <!-- 统计信息 -->
    <view class="statistics">
      <view class="stat-item">
        <text class="stat-number">{{statistics.totalParticipants}}</text>
        <text class="stat-label">参与人数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.totalTests}}</text>
        <text class="stat-label">测试次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.averageScore}}</text>
        <text class="stat-label">平均分</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.highestScore}}</text>
        <text class="stat-label">最高分</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.completionRate}}%</text>
        <text class="stat-label">完成率</text>
      </view>
    </view>
    
    <!-- 标签页导航 -->
    <view class="tab-nav">
      <view class="tab-item {{currentTab === 'overview' ? 'active' : ''}}" bindtap="switchTab" data-tab="overview">
        <text>概览</text>
      </view>
      <view class="tab-item {{currentTab === 'participants' ? 'active' : ''}}" bindtap="switchTab" data-tab="participants">
        <text>参与者</text>
      </view>
      <view wx:if="{{isMultiLevel}}" class="tab-item {{currentTab === 'levels' ? 'active' : ''}}" bindtap="switchTab" data-tab="levels">
        <text>关卡进度</text>
      </view>
    </view>
    
    <!-- 概览标签页 -->
    <view wx:if="{{currentTab === 'overview'}}" class="tab-content">
      <view class="overview-section">
        <view class="section-title">最近参与者</view>
        <view class="recent-participants">
          <view wx:for="{{participants}}" wx:key="openid" wx:if="{{index < 5}}" class="participant-item" bindtap="viewParticipantDetail" data-index="{{index}}">
            <image class="avatar" src="{{item.avatar || '/images/default-avatar.png'}}" />
            <view class="participant-info">
              <text class="nickname">{{item.nickName}}</text>
              <text class="test-info">测试{{item.testCount}}次 | 最高分{{item.bestScore}}</text>
              <text wx:if="{{isMultiLevel}}" class="level-info">第{{item.currentLevel}}关 | 完成{{item.completedLevels.length}}关</text>
            </view>
            <view class="participant-status">
              <text class="latest-score">{{item.latestScore}}分</text>
              <text class="test-time">{{item.latestTestTime}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="overview-section">
        <view class="section-title">测试分析</view>
        <view class="analysis-charts">
          <view class="chart-item">
            <text class="chart-title">分数分布</text>
            <!-- 这里可以添加图表组件 -->
            <view class="chart-placeholder">分数分布图</view>
          </view>
          <view class="chart-item">
            <text class="chart-title">参与趋势</text>
            <!-- 这里可以添加图表组件 -->
            <view class="chart-placeholder">参与趋势图</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 参与者标签页 -->
    <view wx:if="{{currentTab === 'participants'}}" class="tab-content">
      <view wx:if="{{participants.length === 0}}" class="empty-state">
        <text class="empty-icon">👥</text>
        <text class="empty-text">暂无参与者</text>
        <text class="empty-hint">分享测试给朋友，邀请他们来挑战吧！</text>
      </view>
      <view wx:else class="participants-list">
        <view wx:for="{{participants}}" wx:key="openid" class="participant-card" bindtap="viewParticipantDetail" data-index="{{index}}">
          <view class="participant-header">
            <image class="avatar" src="{{item.avatar || '/images/default-avatar.png'}}" />
            <view class="participant-info">
              <text class="nickname">{{item.nickName}}</text>
              <text class="join-time">首次参与: {{item.firstVisitTime}}</text>
            </view>
            <view class="participant-scores">
              <text class="best-score">最高分: {{item.bestScore}}</text>
              <text class="average-score">平均分: {{item.averageScore}}</text>
            </view>
          </view>
          
          <view class="participant-stats">
            <view class="stat-item">
              <text class="stat-label">测试次数</text>
              <text class="stat-value">{{item.testCount}}</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">最新分数</text>
              <text class="stat-value">{{item.latestScore}}</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">最新正确率</text>
              <text class="stat-value">{{item.latestAccuracy}}%</text>
            </view>
            <view wx:if="{{isMultiLevel}}" class="stat-item">
              <text class="stat-label">当前关卡</text>
              <text class="stat-value">第{{item.currentLevel}}关</text>
            </view>
          </view>
          
          <view wx:if="{{isMultiLevel}}" class="level-progress">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{(item.completedLevels.length / totalLevels) * 100}}%;"></view>
            </view>
            <text class="progress-text">{{item.completedLevels.length}}/{{totalLevels}} 关卡完成</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 关卡进度标签页 -->
    <view wx:if="{{currentTab === 'levels' && isMultiLevel}}" class="tab-content">
      <view class="levels-overview">
        <view wx:for="{{levelsData}}" wx:key="levelIndex" class="level-card">
          <view class="level-header">
            <text class="level-title">第{{item.levelIndex}}关</text>
            <text class="level-participants">{{item.completedCount}}/{{item.totalParticipants}}人完成</text>
          </view>

          <view class="level-participants-list">
            <view wx:for="{{item.participants}}" wx:for-item="participant" wx:key="openid" class="level-participant {{participant.isCompleted ? 'completed' : (participant.hasAttempted ? 'attempted' : 'not-started')}}">
              <image class="small-avatar" src="{{participant.avatar || '/images/default-avatar.png'}}" />
              <view class="participant-info">
                <text class="participant-name">{{participant.nickName}}</text>
                <text class="participant-status">{{participant.status}}</text>
              </view>
              <text class="level-score">{{participant.score}}分</text>
            </view>
          </view>
          
          <view class="level-stats">
            <view class="level-stat">
              <text class="stat-label">完成率</text>
              <text class="stat-value">{{item.completionRate}}%</text>
            </view>
            <view class="level-stat">
              <text class="stat-label">平均分</text>
              <text class="stat-value">{{item.averageScore}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="action-btn share-btn" bindtap="shareToOthers">
      <text>分享测试</text>
    </button>
    <button class="action-btn export-btn" bindtap="exportData">
      <text>导出数据</text>
    </button>
    <button class="action-btn delete-btn" bindtap="deleteShare">
      <text>删除分享</text>
    </button>
  </view>
</view>

<!-- 参与者详情弹窗 -->
<view wx:if="{{showDetail}}" class="modal-overlay" bindtap="closeDetail">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{selectedParticipant.nickName}} 的详细信息</text>
      <text class="modal-close" bindtap="closeDetail">×</text>
    </view>
    
    <view class="modal-body">
      <view class="detail-section">
        <view class="detail-title">基本信息</view>
        <view class="detail-row">
          <text class="label">首次参与:</text>
          <text class="value">{{selectedParticipant.firstVisitTime}}</text>
        </view>
        <view class="detail-row">
          <text class="label">最近测试:</text>
          <text class="value">{{selectedParticipant.latestTestTime}}</text>
        </view>
        <view class="detail-row">
          <text class="label">测试次数:</text>
          <text class="value">{{selectedParticipant.testCount}}</text>
        </view>
      </view>
      
      <view class="detail-section">
        <view class="detail-title">成绩统计</view>
        <view class="detail-row">
          <text class="label">最高分:</text>
          <text class="value">{{selectedParticipant.bestScore}}</text>
        </view>
        <view class="detail-row">
          <text class="label">平均分:</text>
          <text class="value">{{selectedParticipant.averageScore}}</text>
        </view>
        <view class="detail-row">
          <text class="label">最新分数:</text>
          <text class="value">{{selectedParticipant.latestScore}}</text>
        </view>
        <view class="detail-row">
          <text class="label">最新正确率:</text>
          <text class="value">{{selectedParticipant.latestAccuracy}}%</text>
        </view>
      </view>
      
      <view wx:if="{{isMultiLevel}}" class="detail-section">
        <view class="detail-title">关卡进度</view>
        <view class="detail-row">
          <text class="label">当前关卡:</text>
          <text class="value">第{{selectedParticipant.currentLevel}}关</text>
        </view>
        <view class="detail-row">
          <text class="label">完成关卡:</text>
          <text class="value">{{selectedParticipant.completedLevels.length}}/{{totalLevels}}</text>
        </view>
        <view class="level-scores">
          <view wx:for="{{selectedParticipant.levelScores}}" wx:key="*this" class="level-score-item">
            <text class="level-name">第{{index}}关:</text>
            <text class="level-score">{{item}}分</text>
          </view>
        </view>
      </view>

      <!-- 多组任务进度 -->
      <view wx:if="{{selectedParticipant.groupProgress}}" class="detail-section">
        <view class="detail-title">任务进度</view>
        <view class="detail-row">
          <text class="label">当前组:</text>
          <text class="value">第{{selectedParticipant.groupProgress.currentGroup}}组</text>
        </view>
        <view class="detail-row">
          <text class="label">完成进度:</text>
          <text class="value">{{selectedParticipant.groupProgress.completedGroups}}/{{selectedParticipant.groupProgress.totalGroups}} ({{selectedParticipant.groupProgress.progressPercentage}}%)</text>
        </view>
        <view class="detail-row">
          <text class="label">状态:</text>
          <text class="value {{selectedParticipant.groupProgress.isCompleted ? 'status-completed' : 'status-progress'}}">
            {{selectedParticipant.groupProgress.isCompleted ? '已完成' : '进行中'}}
          </text>
        </view>
        <!-- 进度条 -->
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{selectedParticipant.groupProgress.progressPercentage}}%"></view>
        </view>
      </view>

      <!-- 所有错词（多组任务） -->
      <view wx:if="{{selectedParticipant.groupProgress && selectedParticipant.groupProgress.allMistakes.length > 0}}" class="detail-section">
        <view class="detail-title">
          <text>累计错词</text>
          <button class="select-all-btn" bindtap="selectAllGroupMistakes">
            {{allGroupMistakesSelected ? '取消全选' : '全选'}}
          </button>
        </view>
        <view class="mistakes-list">
          <view wx:for="{{selectedParticipant.groupProgress.allMistakes}}" wx:key="word" class="mistake-item">
            <checkbox class="mistake-checkbox"
              value="{{item.word}}"
              checked="{{selectedGroupMistakesMap[item.word]}}"
              bindtap="toggleGroupMistakeSelection"
              data-word="{{item.word}}"
              data-index="{{index}}"
            />
            <view class="mistake-content">
              <text class="mistake-word">{{item.word}}</text>
              <view class="mistake-details">
                <text class="mistake-correct">正确答案: {{item.correctAnswer}}</text>
                <text class="mistake-wrong">你的答案: {{item.userAnswer}}</text>
              </view>
            </view>
          </view>
        </view>
        <view wx:if="{{selectedGroupMistakes.length > 0}}" class="mistake-actions">
          <text class="selected-count">已选择 {{selectedGroupMistakes.length}} 个错词</text>
          <button class="create-test-btn" bindtap="showCreateGroupTestOptions">
            生成新测试
          </button>
        </view>
      </view>

      <!-- 最近错词（单组任务） -->
      <view wx:elif="{{selectedParticipant.mistakes.length > 0}}" class="detail-section">
        <view class="detail-title">
          <text>最近错题</text>
          <button class="select-all-btn" bindtap="selectAllMistakes">
            {{allMistakesSelected ? '取消全选' : '全选'}}
          </button>
        </view>
        <view class="mistakes-list">
          <view wx:for="{{selectedParticipant.mistakes}}" wx:key="word" class="mistake-item">
            <checkbox class="mistake-checkbox" 
              value="{{item.word}}"
              checked="{{selectedMistakesMap[item.word]}}"
              bindtap="toggleMistakeSelection"
              data-word="{{item.word}}"
              data-index="{{index}}"
            />
            <view class="mistake-content">
              <text class="mistake-word">{{item.word}}</text>
              <view class="mistake-details">
                <text class="mistake-correct">正确答案: {{item.correctAnswer}}</text>
                <text class="mistake-wrong">你的答案: {{item.userAnswer}}</text>
              </view>
            </view>
          </view>
        </view>
        <view wx:if="{{selectedMistakes.length > 0}}" class="mistake-actions">
          <text class="selected-count">已选择 {{selectedMistakes.length}} 个错词</text>
          <button class="create-test-btn" bindtap="showCreateTestOptions">
            生成新测试
          </button>
        </view>
      </view>
    </view>
  </view>
</view> 