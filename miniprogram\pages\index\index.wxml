<!-- 专业简洁首页 -->
<view class="luxury-container">
  <!-- 顶部横幅 -->
  <view class="luxury-header" bindtap="goToAbout">
    <view class="header-content">
      <view class="brand-section">
        <view class="brand-logo">
          <image class="logo-image" src="/assets/icons/logo.png" mode="aspectFit"></image>
        </view>
        <view class="brand-info">
          <text class="app-title">墨词自习室</text>
          <text class="app-subtitle">智能学习 · 个性发展 · 共创空间</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 通知栏 - 移动到这里 -->
  <view class="notice-container">
    <view class="notice-icon">📢</view>
    <view class="notice-content">
      <view class="notice-text-wrapper">
        <text class="notice-text">{{noticeText}}　　　{{noticeText}}</text>
      </view>
    </view>
  </view>

  <!-- 功能卡片区域 -->
  <view class="luxury-functions">
    <view class="functions-title">
      <text class="title-text">功能探索</text>
      <view class="title-underline"></view>
    </view>
    
    <view class="function-grid">
      <!-- 单词检测 -->
      <view class="luxury-card card-gradient-blue" bindtap="onFunctionTap" data-id="wordtest">
        <view class="card-bg-pattern"></view>
        <view class="card-glow"></view>
        <view class="card-inner">
          <view class="card-icon-container">
            <text class="card-icon">🔍</text>
          </view>
          <view class="card-text">
            <text class="card-title">单词检测</text>
            <text class="card-subtitle">检测词汇掌握情况</text>
          </view>
        </view>
      </view>

      <!-- 短语检测 -->
      <view class="luxury-card card-gradient-green" bindtap="onFunctionTap" data-id="phrasetest">
        <view class="card-bg-pattern"></view>
        <view class="card-glow"></view>
        <view class="card-inner">
          <view class="card-icon-container">
            <text class="card-icon">📝</text>
          </view>
          <view class="card-text">
            <text class="card-title">短语检测</text>
            <text class="card-subtitle">提升短语理解能力</text>
          </view>
        </view>
      </view>

      <!-- 单词竞赛 -->
      <view class="luxury-card card-gradient-purple" bindtap="onFunctionTap" data-id="competition">
        <view class="card-bg-pattern"></view>
        <view class="card-glow"></view>
        <view class="card-inner">
          <view class="card-icon-container">
            <text class="card-icon">🏆</text>
          </view>
          <view class="card-text">
            <text class="card-title">单词竞赛</text>
            <text class="card-subtitle">排名竞技模式</text>
          </view>
        </view>
      </view>

      <!-- 写作积累 -->
      <view class="luxury-card card-gradient-red" bindtap="onFunctionTap" data-id="writing">
        <view class="card-bg-pattern"></view>
        <view class="card-glow"></view>
        <view class="card-inner">
          <view class="card-icon-container">
            <text class="card-icon">✍️</text>
          </view>
          <view class="card-text">
            <text class="card-title">写作积累</text>
            <text class="card-subtitle">素材与范文积累</text>
          </view>
        </view>
      </view>

      <!-- 听口训练 -->
      <view class="luxury-card card-gradient-teal" bindtap="onFunctionTap" data-id="listening">
        <view class="card-bg-pattern"></view>
        <view class="card-glow"></view>
        <view class="card-inner">
          <view class="card-icon-container">
            <text class="card-icon">🎧</text>
          </view>
          <view class="card-text">
            <text class="card-title">听口训练</text>
            <text class="card-subtitle">听力口语能力提升</text>
          </view>
        </view>
      </view>

      <!-- 教师工具箱 -->
      <view class="luxury-card card-gradient-orange" bindtap="onFunctionTap" data-id="teacher-tools">
        <view class="card-bg-pattern"></view>
        <view class="card-glow"></view>
        <view class="card-inner">
          <view class="card-icon-container">
            <text class="card-icon">👨‍🏫</text>
          </view>
          <view class="card-text">
            <text class="card-title">教师工具箱</text>
            <text class="card-subtitle">专业教学辅助工具</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 装饰分割线 -->
  <view class="decorative-divider"></view>

  <!-- 用户信息卡片 - 合并用户信息和励志语句 -->
  <view class="user-info-card">
    <view class="user-greeting-section">
      <view class="greeting-icon">👋</view>
      <view class="greeting-content">
        <!-- 已登录用户显示使用天数 -->
        <text class="user-greeting" style="font-size: {{greetingFontSize}}rpx;" wx:if="{{isLoggedIn && registerDaysText}}">
          <text class="nickname">{{userInfo.wechatInfo.nickName || userInfo.username || '学习者'}}</text>，今天是您使用墨词自习室的第{{registerDaysText}}
        </text>
        <!-- 未登录用户显示欢迎信息 -->
        <text class="user-greeting" style="font-size: {{greetingFontSize}}rpx;" wx:else>
          <text class="nickname">{{userInfo.wechatInfo.nickName || userInfo.username || '学习者'}}</text>，欢迎使用墨词自习室
        </text>
      </view>
    </view>

    <view class="motto-divider"></view>

    <view class="motto-section">
      <view class="motto-icon">✨</view>
      <text class="motto-text">A small step in daily learning, a giant leap in English proficiency</text>
    </view>
  </view>
</view> 