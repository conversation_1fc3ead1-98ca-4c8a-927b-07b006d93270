/* 首页样式 - 扁平风格设计 */
.luxury-container {
  height: 100vh;
  background: #f8fafc;
  padding-bottom: calc(60rpx + env(safe-area-inset-bottom));
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部Banner - 高档立体设计 */
.luxury-header {
  position: relative;
  margin: 28rpx 32rpx 0;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #4f46e5 100%);
  flex: 0 0 300rpx;
  display: flex;
  align-items: center;
  overflow: hidden;
  box-shadow: 
    0 16rpx 48rpx rgba(102, 126, 234, 0.25),
    0 8rpx 24rpx rgba(118, 75, 162, 0.2),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.1);
}

.luxury-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 60%),
    linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.05) 50%, transparent 60%);
  pointer-events: none;
}

.luxury-header::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.08) 50%, transparent 70%);
  transform: rotate(45deg);
  animation: headerShine 6s ease-in-out infinite;
  pointer-events: none;
}

@keyframes headerShine {
  0%, 100% { transform: rotate(45deg) translateX(-150%); opacity: 0; }
  50% { transform: rotate(45deg) translateX(150%); opacity: 1; }
}

.header-glow {
  display: none;
}

.header-content {
  padding: 0 40rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
}

.brand-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.brand-logo {
  position: relative;
  margin-right: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 18rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(15rpx);
  box-shadow: 
    0 8rpx 24rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.brand-logo::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, transparent 60%);
  border-radius: 18rpx;
  pointer-events: none;
}

.logo-image {
  width: 68rpx;
  height: 68rpx;
  border-radius: 12rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.2));
  position: relative;
  z-index: 2;
}

.logo-shine {
  display: none;
}

.brand-info {
  display: flex;
  flex-direction: column;
}

.app-title {
  color: white;
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  letter-spacing: 2rpx;
  text-shadow: 
    0 2rpx 4rpx rgba(0, 0, 0, 0.3),
    0 4rpx 8rpx rgba(0, 0, 0, 0.2),
    0 0 20rpx rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 2;
}

.app-title::after {
  content: '';
  position: absolute;
  bottom: -6rpx;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 20%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.4) 80%, transparent 100%);
  border-radius: 1rpx;
  box-shadow: 0 0 8rpx rgba(255, 255, 255, 0.3);
}

.app-subtitle {
  color: rgba(255, 255, 255, 0.95);
  font-size: 28rpx;
  font-weight: 500;
  text-shadow: 
    0 2rpx 4rpx rgba(0, 0, 0, 0.25),
    0 0 15rpx rgba(255, 255, 255, 0.08);
  letter-spacing: 1rpx;
  position: relative;
  z-index: 2;
}

/* 功能区域 - 扁平风格 */
.luxury-functions {
  flex: 0 0 680rpx;
  padding: 32rpx 32rpx 0;
  display: flex;
  flex-direction: column;
}

.functions-title {
  text-align: left;
  margin-bottom: 32rpx;
  position: relative;
}

.title-text {
  color: #1e293b;
  font-size: 40rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.title-underline {
  position: absolute;
  bottom: -12rpx;
  left: 0;
  width: 80rpx;
  height: 4rpx;
  background: #4f46e5;
  border-radius: 2rpx;
}

.function-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(3, 200rpx);
  gap: 24rpx;
  flex: 1;
}

/* 美化卡片设计 */
.luxury-card {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2rpx solid transparent;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.luxury-card:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.luxury-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

/* 卡片背景装饰 */
.card-bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 卡片光效 */
.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: rotate(45deg);
  animation: cardGlow 4s ease-in-out infinite;
  pointer-events: none;
}

/* 为不同卡片添加延迟，创造错落效果 */
.card-gradient-blue .card-glow { animation-delay: 0s; }
.card-gradient-green .card-glow { animation-delay: 0.5s; }
.card-gradient-purple .card-glow { animation-delay: 1s; }
.card-gradient-red .card-glow { animation-delay: 1.5s; }
.card-gradient-teal .card-glow { animation-delay: 2s; }
.card-gradient-orange .card-glow { animation-delay: 2.5s; }

.card-gradient-blue .card-icon { animation-delay: 0s; }
.card-gradient-green .card-icon { animation-delay: 0.3s; }
.card-gradient-purple .card-icon { animation-delay: 0.6s; }
.card-gradient-red .card-icon { animation-delay: 0.9s; }
.card-gradient-teal .card-icon { animation-delay: 1.2s; }
.card-gradient-orange .card-icon { animation-delay: 1.5s; }

@keyframes cardGlow {
  0%, 100% { transform: rotate(45deg) translateX(-120%); opacity: 0; }
  50% { transform: rotate(45deg) translateX(120%); opacity: 0.8; }
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-3rpx) scale(1.08); }
}

.card-sparkle {
  display: none;
}

.icon-shine {
  display: block;
}

/* 卡片内容 - 横向布局 */
.card-inner {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  z-index: 2;
  padding: 28rpx 32rpx;
  min-height: 200rpx;
  box-sizing: border-box;
}

.card-icon-container {
  position: relative;
  margin-right: 24rpx;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 12rpx;
  padding: 20rpx;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  box-shadow: 
    0 4rpx 12rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  overflow: hidden;
}

.card-icon-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.card-icon {
  font-size: 48rpx;
  display: block;
  line-height: 1;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
  animation: iconFloat 3.5s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-2rpx) scale(1.05); }
}

.card-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex: 1;
  height: 100%;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
  line-height: 1.3;
  letter-spacing: 0.5rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.card-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  font-weight: 400;
  letter-spacing: 0.3rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 美化卡片配色 - 渐变背景 */
.card-gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.card-gradient-green {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-gradient-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.card-gradient-red {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.card-gradient-teal {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.card-gradient-orange {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* 装饰分割线 */
.decorative-divider {
  margin: 32rpx 32rpx 28rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #4f46e5 0%, #06b6d4 25%, #10b981 50%, #f59e0b 75%, #ef4444 100%);
  border-radius: 2rpx;
  position: relative;
  overflow: hidden;
}

.decorative-divider::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 用户信息卡片 - 与功能卡片协调的设计 */
.user-info-card {
  margin: 0 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 36rpx 24rpx 44rpx; /* 增加顶部和底部padding */
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  min-height: 160rpx; /* 设置最小高度 */
}

.user-greeting-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx; /* 增加底部间距 */
  text-align: center;
  min-height: 60rpx; /* 设置最小高度 */
  width: 100%; /* 确保占满宽度 */
  overflow: hidden; /* 防止溢出 */
}

.greeting-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
  animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-10deg); }
  75% { transform: rotate(10deg); }
}

.greeting-content {
  flex: 1; /* 占据剩余空间 */
  overflow: hidden; /* 防止溢出 */
  padding: 0 8rpx; /* 左右留一点间距 */
}

.user-greeting {
  font-size: 30rpx;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.5;
  letter-spacing: 0.3rpx;
  white-space: nowrap; /* 强制单行显示 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 超长显示省略号 */
  width: 100%; /* 占满容器宽度 */
  text-align: center; /* 居中对齐 */
}

.user-greeting .nickname {
  color: #667eea;
  font-weight: 600;
}

.motto-divider {
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #d1d5db 50%, #e5e7eb 80%, transparent 100%);
  margin: 28rpx 0; /* 增加上下间距 */
}

.motto-section {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 50rpx; /* 设置最小高度 */
  padding-bottom: 4rpx; /* 增加底部间距 */
}

.motto-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
  opacity: 0.8;
}

.motto-text {
  font-size: 22rpx; /* 从20rpx调整到22rpx */
  color: #6b7280;
  font-style: italic;
  font-weight: 400;
  line-height: 1.4;
  letter-spacing: 0.1rpx;
  white-space: nowrap; /* 确保一行显示 */
  overflow: hidden; /* 防止溢出 */
  text-overflow: ellipsis; /* 超长显示省略号 */
}





/* 通知栏样式 - 现代简约设计 */
.notice-container {
  margin: 20rpx 32rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);
  border: 1rpx solid rgba(14, 165, 233, 0.2);
  border-radius: 16rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-shadow:
    0 4rpx 16rpx rgba(14, 165, 233, 0.1),
    0 2rpx 8rpx rgba(2, 132, 199, 0.08);
  overflow: hidden;
  position: relative;
}

.notice-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, #0ea5e9 20%, #0284c7 50%, #0ea5e9 80%, transparent 100%);
  animation: notice-flow 4s ease-in-out infinite;
}

@keyframes notice-flow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

.notice-icon {
  flex: 0 0 auto;
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #0284c7;
  animation: notice-bounce 3s ease-in-out infinite;
}

@keyframes notice-bounce {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-2rpx) scale(1.05);
  }
}

.notice-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  position: relative;
}

.notice-text-wrapper {
  white-space: nowrap;
  display: inline-block;
  animation: continuous-scroll 25s linear infinite;
}

/* 无缝滚动：从喇叭旁边开始，滚动到一半时显示第二遍文字 */
@keyframes continuous-scroll {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

.notice-text {
  color: #075985;
  font-size: 26rpx;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}





/* 隐藏旧的框内励志语句样式 */
.motto-section-inside {
  display: none;
}

/* 隐藏复杂的统计信息和头像相关样式 */
.user-details,
.user-name,
.user-badges,
.stats-container,
.motto-icon,
.user-avatar-section,
.avatar-container,
.user-avatar,
.avatar-ring,
.avatar-glow {
  display: none;
}

.motto-decoration {
  display: none;
}

/* 去除所有动画 */
@keyframes gradientShift,
@keyframes glowRotate,
@keyframes shine,
@keyframes ringRotate,
@keyframes pulse,
@keyframes iconShine,
@keyframes sparkle,
@keyframes panelShimmer,
@keyframes twinkle,
@keyframes cardFloat {
  to { opacity: 1; transform: none; }
}

.luxury-card {
  opacity: 1;
  transform: none;
  animation: none;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .luxury-header {
    margin: 24rpx 28rpx 0;
    flex: 0 0 260rpx;
    border-radius: 18rpx;
    box-shadow: 
      0 12rpx 36rpx rgba(102, 126, 234, 0.2),
      0 6rpx 18rpx rgba(118, 75, 162, 0.15),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.15),
      inset 0 -1rpx 0 rgba(0, 0, 0, 0.08);
  }
  
  .header-content {
    padding: 0 32rpx;
  }
  
  .brand-logo {
    padding: 20rpx;
    margin-right: 28rpx;
    border-radius: 16rpx;
    box-shadow: 
      0 6rpx 18rpx rgba(0, 0, 0, 0.12),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.25),
      inset 0 -1rpx 0 rgba(0, 0, 0, 0.08);
  }
  
  .logo-image {
    width: 58rpx;
    height: 58rpx;
    border-radius: 10rpx;
    filter: drop-shadow(0 3rpx 6rpx rgba(0, 0, 0, 0.18));
  }
  
  .app-title {
    font-size: 40rpx;
    margin-bottom: 10rpx;
  }
  
  .app-subtitle {
    font-size: 24rpx;
  }
  
  .luxury-functions {
    flex: 0 0 620rpx;
    padding: 28rpx 28rpx 0;
  }
  
  .functions-title {
    margin-bottom: 28rpx;
  }
  
  .title-text {
    font-size: 34rpx;
  }
  
  .function-grid {
    grid-template-rows: repeat(3, 185rpx);
    gap: 20rpx;
  }
  
  .card-inner {
    padding: 24rpx 28rpx;
    min-height: 185rpx;
  }
  
    .card-icon-container {
    margin-right: 20rpx;
    padding: 18rpx;
    width: 72rpx;
    height: 72rpx;
    border-radius: 10rpx;
  }

  .card-icon {
    font-size: 42rpx;
  }

  @keyframes cardGlow {
    0%, 100% { transform: rotate(45deg) translateX(-100%); opacity: 0; }
    50% { transform: rotate(45deg) translateX(80%); opacity: 0.8; }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-2rpx) scale(1.05); }
  }
  
  .card-title {
    font-size: 28rpx;
    margin-bottom: 6rpx;
  }
  
  .card-subtitle {
    font-size: 21rpx;
  }
  
  .decorative-divider {
    margin: 28rpx 28rpx 24rpx;
    height: 3rpx;
  }

  .user-info-card {
    margin: 0 28rpx 20rpx;
    padding: 32rpx 20rpx 40rpx; /* 增加顶部和底部padding */
    border-radius: 16rpx;
    min-height: 140rpx; /* 设置最小高度 */
  }

  .user-greeting {
    font-size: 26rpx;
    white-space: nowrap; /* 强制单行显示 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 超长显示省略号 */
  }

  .greeting-icon {
    font-size: 32rpx;
    margin-right: 10rpx;
  }

  .motto-text {
    font-size: 20rpx; /* 从18rpx调整到20rpx */
  }

  .motto-icon {
    font-size: 24rpx;
    margin-right: 10rpx;
  }

  .notice-container {
    margin: 16rpx 28rpx;
    height: 68rpx;
    padding: 0 18rpx;
    border-radius: 14rpx;
  }

  .notice-icon {
    font-size: 28rpx;
    margin-right: 14rpx;
  }

  .notice-text {
    font-size: 22rpx;
  }
}

@media (max-height: 800px) {
  .luxury-header {
    margin: 20rpx 28rpx 0;
    flex: 0 0 240rpx;
    border-radius: 16rpx;
    box-shadow: 
      0 10rpx 30rpx rgba(102, 126, 234, 0.18),
      0 5rpx 15rpx rgba(118, 75, 162, 0.12),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.12),
      inset 0 -1rpx 0 rgba(0, 0, 0, 0.06);
  }
  
  .header-content {
    padding: 0 32rpx;
  }
  
  .brand-logo {
    padding: 18rpx;
    margin-right: 24rpx;
    border-radius: 14rpx;
    box-shadow: 
      0 5rpx 15rpx rgba(0, 0, 0, 0.1),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
      inset 0 -1rpx 0 rgba(0, 0, 0, 0.06);
  }
  
  .logo-image {
    width: 56rpx;
    height: 56rpx;
    border-radius: 10rpx;
    filter: drop-shadow(0 2rpx 5rpx rgba(0, 0, 0, 0.15));
  }
  
  .app-title {
    font-size: 38rpx;
    margin-bottom: 8rpx;
  }
  
  .app-subtitle {
    font-size: 22rpx;
  }
  
  .luxury-functions {
    flex: 0 0 580rpx;
    padding: 24rpx 28rpx 0;
  }
  
  .functions-title {
    margin-bottom: 24rpx;
  }
  
  .title-text {
    font-size: 32rpx;
  }
  
  .function-grid {
    grid-template-rows: repeat(3, 175rpx);
    gap: 18rpx;
  }
  
  .card-inner {
    padding: 20rpx 24rpx;
    min-height: 175rpx;
  }
  
  .card-icon-container {
    margin-right: 18rpx;
    padding: 16rpx;
    width: 68rpx;
    height: 68rpx;
    border-radius: 10rpx;
  }
  
    .card-icon {
    font-size: 40rpx;
  }

  @keyframes cardGlow {
    0%, 100% { transform: rotate(45deg) translateX(-100%); opacity: 0; }
    50% { transform: rotate(45deg) translateX(70%); opacity: 0.7; }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-2rpx) scale(1.04); }
  }

  .card-title {
    font-size: 26rpx;
    margin-bottom: 6rpx;
  }
  
  .card-subtitle {
    font-size: 19rpx;
  }
  
  .decorative-divider {
    margin: 24rpx 28rpx 20rpx;
    height: 3rpx;
  }

  .user-info-card {
    margin: 0 28rpx 18rpx;
    padding: 30rpx 18rpx 38rpx; /* 增加顶部和底部padding */
    border-radius: 14rpx;
    min-height: 120rpx; /* 设置最小高度 */
  }

  .user-greeting {
    font-size: 24rpx;
    white-space: nowrap; /* 强制单行显示 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 超长显示省略号 */
  }

  .greeting-icon {
    font-size: 30rpx;
    margin-right: 8rpx;
  }

  .motto-text {
    font-size: 18rpx; /* 从16rpx调整到18rpx */
  }

  .motto-icon {
    font-size: 22rpx;
    margin-right: 8rpx;
  }

  .notice-container {
    margin: 14rpx 28rpx;
    height: 64rpx;
    padding: 0 16rpx;
    border-radius: 12rpx;
  }

  .notice-icon {
    font-size: 26rpx;
    margin-right: 12rpx;
  }

  .notice-text {
    font-size: 20rpx;
  }
} 