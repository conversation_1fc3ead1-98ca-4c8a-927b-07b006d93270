Page({
  data: {
    exercises: [
      {
        id: 1,
        title: '听后选择',
        subtitle: '共两节，共14道小题',
        description: '听对话和独白，选择最佳答案',
        icon: '🎧',
        difficulty: '★★☆',
        time: '15分钟',
        score: '21分',
        route: '/pages/listening/listen-choose/listen-choose'
      },
      {
        id: 2,
        title: '听后记录与转述',
        subtitle: '共两节，共15分',
        description: '听短文填空并转述内容',
        icon: '📝',
        difficulty: '★★★',
        time: '8分钟',
        score: '15分',
        route: '/pages/listening/listen-record/listen-record'
      },
      {
        id: 3,
        title: '朗读短文并回答问题',
        subtitle: '共两节，共14分',
        description: '朗读短文并口头回答问题',
        icon: '📖',
        difficulty: '★★☆',
        time: '10分钟',
        score: '14分',
        route: '/pages/listening/read-answer/read-answer'
      }
    ],
    tips: [
      '建议按顺序完成三个题型的练习',
      '每个题型都有详细的时间限制',
      '注意听力材料的播放次数限制',
      '录音功能需要授权麦克风权限'
    ]
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '听口训练'
    });
  },

  // 跳转到音标专练
  goToPhonetic() {
    wx.navigateTo({
      url: '/pages/phonetic/practice/practice'
    });
  },

  // 跳转到具体练习
  goToExercise(e) {
    const route = e.currentTarget.dataset.route;
    wx.navigateTo({
      url: route
    });
  },

  // 查看练习记录
  viewRecords() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 快速练习（随机选择一个题型）
  quickPractice() {
    const exercises = this.data.exercises;
    const randomIndex = Math.floor(Math.random() * exercises.length);
    const selectedExercise = exercises[randomIndex];
    
    wx.navigateTo({
      url: selectedExercise.route
    });
  }
}); 