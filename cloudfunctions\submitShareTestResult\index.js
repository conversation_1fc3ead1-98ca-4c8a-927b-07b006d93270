const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  
  try {
    const { 
      shareId, 
      levelId = 1,  // 关卡ID，默认为1
      testResult,   // 测试结果 { score, accuracy, correctCount, wrongCount, mistakes, timeSpent }
      userInfo      // 用户信息
    } = event;
    
    if (!shareId || !testResult) {
      return {
        success: false,
        message: '缺少必要参数'
      };
    }
    
    const currentUser = wxContext.OPENID;
    
    // 获取分享测试数据
    const shareTestResult = await db.collection('shareTests')
      .where({
        shareId: shareId
      })
      .get();
    
    if (!shareTestResult.data || shareTestResult.data.length === 0) {
      return {
        success: false,
        message: '分享测试不存在'
      };
    }
    
    const shareTestData = shareTestResult.data[0];
    
    // 检查是否过期
    if (shareTestData.expireTime) {
      const now = new Date();
      const expireTime = new Date(shareTestData.expireTime);
      if (now > expireTime) {
        return {
          success: false,
          message: '分享测试已过期'
        };
      }
    }
    
    // 准备更新数据
    const updateData = {};
    
    // 1. 更新测试结果记录
    const resultRecord = {
      ...testResult,
      shareId: shareId,
      levelId: levelId,
      participantOpenid: currentUser,
      participantInfo: userInfo,
      submitTime: db.serverDate(),
      testMode: shareTestData.testType
    };
    
    updateData['results'] = db.command.push(resultRecord);
    
    // 2. 更新访问者信息
    const existingVisitorIndex = shareTestData.visitors.findIndex(v => v.openid === currentUser);
    if (existingVisitorIndex >= 0) {
      const visitor = shareTestData.visitors[existingVisitorIndex];
      const updatedVisitor = {
        ...visitor,
        // 更新用户信息（可能用户修改了昵称或头像）
        nickName: userInfo.nickName || visitor.nickName || '匿名用户',
        avatar: userInfo.avatarUrl || visitor.avatar || '',
        lastTestTime: new Date(),
        testCount: (visitor.testCount || 0) + 1,
        bestScore: Math.max(visitor.bestScore || 0, testResult.score),
        latestScore: testResult.score,
        latestAccuracy: testResult.accuracy
      };

      updateData[`visitors.${existingVisitorIndex}`] = updatedVisitor;
    } else {
      // 如果是新用户，添加到访问者列表
      const newVisitor = {
        openid: currentUser,
        nickName: userInfo.nickName || '匿名用户',
        avatar: userInfo.avatarUrl || '',
        firstVisitTime: new Date(),
        lastTestTime: new Date(),
        visitCount: 1,
        testCount: 1,
        bestScore: testResult.score,
        latestScore: testResult.score,
        latestAccuracy: testResult.accuracy
      };

      updateData['visitors'] = db.command.push(newVisitor);
    }
    
    // 3. 更新关卡进度（如果是多关卡测试）
    if (shareTestData.isMultiLevel) {
      const currentProgress = shareTestData.levelProgress[currentUser] || {
        currentLevel: 1,
        completedLevels: [],
        scores: {}
      };
      
      // 更新当前关卡分数
      currentProgress.scores[levelId] = testResult.score;
      
      // 如果通过了当前关卡（假设80分以上算通过）
      if (testResult.score >= 80) {
        if (!currentProgress.completedLevels.includes(levelId)) {
          currentProgress.completedLevels.push(levelId);
        }
        
        // 解锁下一关卡
        if (levelId < shareTestData.totalLevels) {
          currentProgress.currentLevel = Math.max(currentProgress.currentLevel, levelId + 1);
        }
      }
      
      updateData[`levelProgress.${currentUser}`] = currentProgress;
    }
    
    // 执行更新
    await db.collection('shareTests').doc(shareTestData._id).update({
      data: updateData
    });
    
    // 返回结果，包含是否解锁新关卡的信息
    const response = {
      success: true,
      data: {
        submitted: true,
        score: testResult.score,
        accuracy: testResult.accuracy
      }
    };
    
    if (shareTestData.isMultiLevel) {
      const progress = updateData[`levelProgress.${currentUser}`];
      response.data.levelProgress = {
        currentLevel: progress.currentLevel,
        completedLevels: progress.completedLevels,
        totalLevels: shareTestData.totalLevels,
        isNewLevelUnlocked: testResult.score >= 80 && levelId < shareTestData.totalLevels
      };
    }
    
    return response;
    
  } catch (error) {
    console.error('提交分享测试结果失败:', error);
    return {
      success: false,
      message: '提交测试结果失败',
      error: error
    };
  }
}; 