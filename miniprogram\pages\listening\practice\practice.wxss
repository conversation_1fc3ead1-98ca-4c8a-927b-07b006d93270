.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 进度条样式 */
.progress-bar {
  height: 6rpx;
  background-color: #ddd;
  border-radius: 3rpx;
  margin-bottom: 30rpx;
  position: relative;
}

.progress-inner {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 3rpx;
  transition: width 0.3s;
}

.progress-text {
  position: absolute;
  right: 0;
  top: 20rpx;
  font-size: 24rpx;
  color: #666;
}

/* 练习卡片样式 */
.practice-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 音频区域样式 */
.audio-section {
  margin-bottom: 40rpx;
}

.audio-header {
  margin-bottom: 30rpx;
}

.audio-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.audio-subtitle {
  font-size: 28rpx;
  color: #666;
}

.audio-player {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.play-btn {
  width: 100rpx;
  height: 100rpx;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.play-btn.playing {
  background-color: #f44336;
}

.play-icon {
  width: 48rpx;
  height: 48rpx;
}

.audio-slider {
  flex: 1;
}

.time-text {
  font-size: 24rpx;
  color: #666;
  min-width: 100rpx;
  text-align: right;
}

/* 选项区域样式 */
.options-section {
  margin-bottom: 40rpx;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  transition: all 0.3s;
}

.option-item.selected {
  background-color: #e8f5e9;
  border: 2rpx solid #4CAF50;
}

.option-item.correct {
  background-color: #e8f5e9;
  border: 2rpx solid #4CAF50;
}

.option-item.wrong {
  background-color: #ffebee;
  border: 2rpx solid #f44336;
}

.option-text {
  font-size: 32rpx;
  color: #333;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: center;
}

.action-btn {
  width: 300rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #4CAF50;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* 完成提示样式 */
.completion-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-body {
  padding: 30rpx;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 32rpx;
  color: #666;
}

.stat-value {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.modal-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.modal-btn:first-child {
  background-color: #4CAF50;
  color: #fff;
}

.modal-btn:nth-child(2) {
  background-color: #fff;
  color: #4CAF50;
  border: 2rpx solid #4CAF50;
}

.modal-btn:last-child {
  background-color: #f5f5f5;
  color: #666;
} 